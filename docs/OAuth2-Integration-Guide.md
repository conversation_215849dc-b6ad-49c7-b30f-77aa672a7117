# OAuth2 Resource Server Integration with Keycloak

This document describes the OAuth2 resource server integration implemented in the SMAILE Health platform, providing JWT-based authentication alongside the existing header-based authentication system.

## Overview

The OAuth2 integration provides:
- **Dual Authentication Support**: Both header-based (existing) and JWT-based (new) authentication
- **Keycloak Integration**: Full compatibility with Keycloak as the authorization server
- **Role-Based Authorization**: Support for `@PreAuthorize` annotations with Keycloak roles
- **Production-Ready**: Comprehensive error handling, logging, and security features

## Architecture

### Components

1. **KeycloakJwtAuthenticationConverter**: Converts Keycloak JWT tokens to Spring Security authorities
2. **OAuth2AuthenticationEntryPoint**: Handles authentication failures with structured error responses
3. **OAuth2AccessDeniedHandler**: Manages authorization failures with detailed error information
4. **WebSecurityConfig**: Updated to support both authentication methods

### Authentication Flow

```mermaid
graph TD
    A[Client Request] --> B{Has Authorization Header?}
    B -->|Yes| C{Bearer Token?}
    B -->|No| D[Header-based Auth Filter]
    C -->|Yes| E[JWT Authentication]
    C -->|No| D
    E --> F[Keycloak JWT Converter]
    F --> G[Extract Roles & Authorities]
    G --> H[Set Security Context]
    D --> I[Extract Headers]
    I --> J[SMAILE Authentication Provider]
    J --> H
    H --> K[Authorize Request]
```

## Configuration

### Dependencies

The following dependencies are added to `pom.xml`:

```xml
<!-- OAuth2 Resource Server -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
</dependency>

<!-- JWT Support -->
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-api</artifactId>
    <version>0.12.6</version>
</dependency>
```

### Application Properties

#### Development (`application-dev.yml`)
```yaml
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://iam.smaile.egs-dev.site/realms/smaile
          audiences: smaile-be,account
          jwk-set-uri: https://iam.smaile.egs-dev.site/realms/smaile/protocol/openid-connect/certs
```

#### Production (`application-prod.yml`)
```yaml
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${IAM_ENDPOINT}/realms/${IAM_REALM}
          audiences: ${OAUTH2_AUDIENCES:smaile-be,account}
          jwk-set-uri: ${IAM_ENDPOINT}/realms/${IAM_REALM}/protocol/openid-connect/certs
```

## Usage

### Client Authentication

#### JWT Token Authentication
```bash
curl -H "Authorization: Bearer <jwt-token>" \
     -H "Content-Type: application/json" \
     https://api.smaile.com/api/v1/users/123
```

#### Header-based Authentication (Existing)
```bash
curl -H "x-forwarded-smaile-user: <keycloak-id>" \
     -H "x-forwarded-email: <email>" \
     -H "Content-Type: application/json" \
     https://api.smaile.com/api/v1/users/123
```

### Role-Based Authorization

Controllers now support `@PreAuthorize` annotations:

```java
@GetMapping("/{id}")
@PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SMAILE-BE_USER')")
public ResponseEntity<UserResponse> getUser(@PathVariable UUID id) {
    // Implementation
}

@PostMapping
@PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('SMAILE-BE_ADMIN')")
public ResponseEntity<BaseResponse<UUID>> createMedicalProvider(@RequestBody @Valid CreateMedicalProviderRequest request) {
    // Implementation
}
```

### Supported Roles

The system recognizes the following role patterns:

#### Realm Roles (from `realm_access.roles`)
- `admin` → `ROLE_ADMIN`
- `user` → `ROLE_USER`
- `super_admin` → `ROLE_SUPER_ADMIN`

#### Resource Roles (from `resource_access.{client-id}.roles`)
- `smaile-be.admin` → `ROLE_SMAILE-BE_ADMIN`
- `smaile-be.user` → `ROLE_SMAILE-BE_USER`

## Error Handling

### Authentication Errors (401)

```json
{
  "success": false,
  "message": "Authentication failed",
  "data": {
    "timestamp": "2025-08-27T10:30:00Z",
    "status": 401,
    "error": "Unauthorized",
    "error_code": "invalid_token",
    "error_description": "The JWT token is expired",
    "path": "/api/v1/users/123"
  }
}
```

### Authorization Errors (403)

```json
{
  "success": false,
  "message": "Access denied",
  "data": {
    "timestamp": "2025-08-27T10:30:00Z",
    "status": 403,
    "error": "Forbidden",
    "error_code": "access_denied",
    "error_description": "Insufficient permissions to access this resource",
    "user": "<EMAIL>",
    "authorities": ["ROLE_USER"],
    "path": "/api/v1/medical-providers"
  }
}
```

## Testing

### Integration Tests

Run OAuth2 integration tests:

```bash
mvn test -Dtest=OAuth2IntegrationTest
```

### Unit Tests

Test JWT authentication converter:

```bash
mvn test -Dtest=KeycloakJwtAuthenticationConverterTest
```

### Manual Testing

1. **Obtain JWT Token from Keycloak**:
   ```bash
   curl -X POST "https://iam.smaile.egs-dev.site/realms/smaile/protocol/openid-connect/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "grant_type=password" \
        -d "client_id=smaile-be" \
        -d "client_secret=<client-secret>" \
        -d "username=<username>" \
        -d "password=<password>"
   ```

2. **Test API Endpoints**:
   ```bash
   # Test with JWT token
   curl -H "Authorization: Bearer <jwt-token>" \
        https://api.smaile.com/api/v1/users/123
   
   # Test role-based authorization
   curl -H "Authorization: Bearer <admin-jwt-token>" \
        -X POST https://api.smaile.com/api/v1/medical-providers \
        -H "Content-Type: application/json" \
        -d '{}'
   ```

## Security Considerations

1. **Token Validation**: All JWT tokens are validated against Keycloak's public keys
2. **Audience Validation**: Tokens must include the correct audience claim
3. **Expiration Checking**: Expired tokens are automatically rejected
4. **Role Mapping**: Keycloak roles are properly mapped to Spring Security authorities
5. **Error Handling**: Sensitive information is not exposed in error responses
6. **Logging**: All authentication attempts are logged for security monitoring

## Troubleshooting

### Common Issues

1. **Invalid JWT Token**:
   - Verify token is not expired
   - Check token signature against Keycloak public keys
   - Ensure correct audience claim

2. **Role Authorization Failures**:
   - Verify user has required roles in Keycloak
   - Check role mapping in JWT token claims
   - Ensure `@PreAuthorize` expressions are correct

3. **Configuration Issues**:
   - Verify `issuer-uri` points to correct Keycloak realm
   - Check `jwk-set-uri` is accessible
   - Ensure audience configuration matches token claims

### Debug Logging

Enable debug logging for OAuth2:

```yaml
logging:
  level:
    org.springframework.security.oauth2: DEBUG
    com.smaile.health.security.oauth2: DEBUG
```

## Migration Guide

### For Existing Clients

1. **No Breaking Changes**: Existing header-based authentication continues to work
2. **Gradual Migration**: Clients can migrate to JWT tokens at their own pace
3. **Dual Support**: Both authentication methods are supported simultaneously

### For New Clients

1. **Recommended**: Use JWT token authentication for new integrations
2. **Better Security**: JWT tokens provide better security and standardization
3. **Role-Based Access**: Take advantage of fine-grained role-based authorization

## Performance Considerations

1. **Token Validation**: JWT tokens are validated locally using cached public keys
2. **No Database Calls**: JWT authentication doesn't require database lookups
3. **Caching**: Keycloak public keys are cached to reduce network calls
4. **Stateless**: JWT tokens enable truly stateless authentication

## Future Enhancements

1. **Organization Context**: Integration with existing organization permission system
2. **Custom Claims**: Support for custom claims in JWT tokens
3. **Token Refresh**: Automatic token refresh capabilities
4. **Audit Logging**: Enhanced audit logging for compliance requirements
