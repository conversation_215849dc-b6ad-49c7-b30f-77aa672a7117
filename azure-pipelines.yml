trigger:
  branches:
    include:
      - dev
      - init/aks-smaile-test

variables:
  - group: 'ACR login'  #  ACR_USERNAME, ACR_PASSWORD, ACR_LOGIN_SERVER
  - name: imageName
    value: 'smaile-backend'
  # - name: imageTag
  #   value: '$(Build.BuildId)'

stages:
  - stage: BuildAndPush
    displayName: 'Build and Push Docker Image'
    jobs:
      - job: DockerBuild
        displayName: 'Build JAR and Docker Image'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          # Step 0: install Java 17
          - task: JavaToolInstaller@0
            displayName: 'Install JDK 17'
            inputs:
              versionSpec: '17'
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'

          # Step 1: Build JAR with Maven
          - task: Maven@3
            displayName: 'Build Spring Boot App'
            inputs:
              mavenPomFile: 'pom.xml'
              goals: 'clean package'
              options: '-DskipTests'

          # Get commit id
          - script: |
              COMMIT_ID=$(Build.SourceVersion)
              SHORT_COMMIT=${COMMIT_ID:0:7}
              echo "Using commit id: $SHORT_COMMIT"
              echo "##vso[task.setvariable variable=imageTag]$SHORT_COMMIT"
            displayName: 'Set imageTag to short commit id'
                          
          # Step 2: Docker login ACR
          - script: |
              echo "$(ACR_PASSWORD)" | docker login $(ACR_LOGIN_SERVER) -u $(ACR_USERNAME) --password-stdin
            displayName: 'Docker Login to ACR'

          # Step 3: Build Docker image from Dockerfile 
          - script: |
              docker build -t $(ACR_LOGIN_SERVER)/$(imageName):$(imageTag) -t $(ACR_LOGIN_SERVER)/$(imageName):latest .
            displayName: 'Docker Build'

          # Step 4: Push image lên ACR
          - script: |
              docker push $(ACR_LOGIN_SERVER)/$(imageName):$(imageTag)
              docker push $(ACR_LOGIN_SERVER)/$(imageName):latest
            displayName: 'Docker Push to ACR'

#CD for AKS:

  - stage: DeployToAKS
    displayName: 'Deploy to AKS using Helm'
    dependsOn: BuildAndPush
    jobs:
      - job: HelmDeploy
        displayName: 'Helm Upgrade/Install'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: AzureCLI@2
            displayName: 'Azure CLI - Helm Deploy'
            inputs:
              azureSubscription: 'azure-connection-smaile'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az aks get-credentials --resource-group SMAILE-Cluster --name aks-smaile-test --overwrite-existing
                helm upgrade --install smaile-be ./helm-chart \
                  --namespace smaile \
                  --create-namespace \
                  --set image.repository=$(ACR_LOGIN_SERVER)
