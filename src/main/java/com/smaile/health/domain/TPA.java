package com.smaile.health.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "tpa_organizations")
@PrimaryKeyJoinColumn(name = "id")
@Getter
@Setter
public class TPA extends Organization {

    @Column(name = "country", nullable = false)
    private String country;

}
