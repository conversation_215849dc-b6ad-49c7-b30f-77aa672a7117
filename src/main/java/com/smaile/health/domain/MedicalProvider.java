package com.smaile.health.domain;

import com.smaile.health.model.MedicalProviderLicense;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.List;

@Entity
@Table(name = "medical_providers")
@PrimaryKeyJoinColumn(name = "id")
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class MedicalProvider extends Organization {

    @Column(nullable = false)
    private String providerType;

    @Column
    private String market;

    @Column
    private String country;

    @Column
    @JdbcTypeCode(SqlTypes.JSON)
    private List<String> specialities;

    @Column
    @JdbcTypeCode(SqlTypes.JSON)
    private List<MedicalProviderLicense> licenses;

}
