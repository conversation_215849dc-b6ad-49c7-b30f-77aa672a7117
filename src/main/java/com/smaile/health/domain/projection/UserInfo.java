package com.smaile.health.domain.projection;

import com.smaile.health.constants.UserStatus;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/8/2025
 **/
public interface UserInfo {

    UUID getId();

    UUID getKeycloakId();

    String getEmail();

    String getFullName();

    String getPhone();

    UserStatus getStatus();

    OrganizationInfo getOwnerOrganization();

    List<OrganizationInfo> getDelegateOrganizations();

}
