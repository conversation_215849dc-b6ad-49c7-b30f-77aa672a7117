package com.smaile.health.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.model.response.ErrorResponse;
import com.smaile.health.security.authentication.SmaileAuthenticationFilter;
import com.smaile.health.security.authentication.SmaileAuthenticationProvider;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.io.IOException;

import static com.smaile.health.config.GlobalExceptionHandler.ACCESS_DENIED_CODE;
import static com.smaile.health.config.GlobalExceptionHandler.ACCESS_DENIED_MESSAGE;
import static com.smaile.health.config.GlobalExceptionHandler.SMAILE_AUTHENTICATION_ERROR_CODE;
import static com.smaile.health.config.GlobalExceptionHandler.SMAILE_AUTHENTICATION_ERROR_MESSAGE;

/**
 * Web Security Configuration for SMAILE Health platform.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, prePostEnabled = true)
@Profile("!test")
@RequiredArgsConstructor
public class WebSecurityConfig {

    public static final String[] WHITELIST = {
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/utilities/**",
            "/register/**",
            "/actuator/**"
    };
    private final SmaileAuthenticationProvider authenticationProvider;
    private final SmaileAuthenticationFilter authenticationFilter;
    private final ObjectMapper objectMapper;
    private final CorsProperties corsProperties;

    /**
     * Configures the authentication manager with custom authentication provider.
     *
     * @return AuthenticationManager configured with SmaileAuthenticationProvider
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        return new ProviderManager(authenticationProvider);
    }

    /**
     * Configures the main security filter chain with support for both header-based
     * and OAuth2 JWT authentication.
     *
     * @param http HttpSecurity configuration
     * @return SecurityFilterChain configured for SMAILE Health
     * @throws Exception if configuration fails
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .csrf(AbstractHttpConfigurer::disable)
                .exceptionHandling(exceptionHandling -> exceptionHandling
                        .authenticationEntryPoint(authenticationEntryPoint())
                        .accessDeniedHandler(accessDeniedHandler())
                )
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(WHITELIST).permitAll()
                        .anyRequest().authenticated()
                )
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                );

        return http.build();
    }

    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return (HttpServletRequest request, HttpServletResponse response,
                AuthenticationException authException) -> writeErrorResponse(response, HttpStatus.UNAUTHORIZED,
                SMAILE_AUTHENTICATION_ERROR_CODE,
                SMAILE_AUTHENTICATION_ERROR_MESSAGE,
                "Authentication is required to access this resource",
                request.getRequestURI());
    }

    @Bean
    public AccessDeniedHandler accessDeniedHandler() {
        return (HttpServletRequest request, HttpServletResponse response,
                AccessDeniedException accessDeniedException) -> writeErrorResponse(response, HttpStatus.FORBIDDEN,
                ACCESS_DENIED_CODE,
                ACCESS_DENIED_MESSAGE,
                "Insufficient permissions for the requested operation",
                request.getRequestURI());
    }

    /**
     * Helper method to write error responses in a consistent format.
     */
    private void writeErrorResponse(HttpServletResponse response, HttpStatus status,
                                    String errorCode, String message, String detail, String path) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(status.value());
        ErrorResponse errorResponse = ErrorResponse.of(status.value(), errorCode, message, detail, path);
        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        configuration.setAllowedOrigins(corsProperties.getAllowedOrigins());
        configuration.setAllowedMethods(corsProperties.getAllowedMethods());
        configuration.setAllowedHeaders(corsProperties.getAllowedHeaders());
        configuration.setAllowCredentials(corsProperties.isAllowCredentials());
        configuration.setMaxAge(corsProperties.getMaxAge());

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

}
