package com.smaile.health.config;

import com.azure.communication.email.EmailClient;
import com.azure.communication.email.EmailClientBuilder;
import com.azure.core.credential.AzureKeyCredential;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.util.StringUtils;

@Configuration
@ConfigurationProperties(prefix = "email")
@Data
public class EmailConfig {

    private Azure azure = new Azure();
    private Support support = new Support();

    @Bean
    @Primary
    public EmailClient azureEmailClient() {
        if (!StringUtils.hasText(azure.getAccessKey()) || !StringUtils.hasText(azure.getEndpoint())) {
            throw new IllegalStateException("Azure Communication Services configuration is incomplete");
        }

        return new EmailClientBuilder()
                .endpoint(azure.getEndpoint())
                .credential(new AzureKeyCredential(azure.getAccessKey()))
                .buildClient();
    }

    @Data
    public static class Azure {

        private String endpoint;
        private String accessKey;
        private String senderAddress;

    }

    @Data
    public static class Support {

        private String email;
        private String phone;

    }

}
