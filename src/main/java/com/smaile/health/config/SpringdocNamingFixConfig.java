package com.smaile.health.config;

import com.google.common.base.CaseFormat;
import io.swagger.v3.oas.models.media.Schema;
import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.LinkedHashMap;

@Configuration
public class SpringdocNamingFixConfig {

    @Bean
    public OpenApiCustomizer forceSnakeCaseInSchemas() {
        return openApi -> openApi.getComponents().getSchemas()
                .values()
                .forEach(schema -> {
                    if (schema.getProperties() == null) {
                        return;
                    }
                    var originalProps = new LinkedHashMap<>(schema.getProperties());
                    schema.getProperties().clear();
                    originalProps.forEach((name, value) -> {
                        String snake = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, name.toString());
                        schema.addProperty(snake, (Schema) value);
                    });
                });
    }

}
