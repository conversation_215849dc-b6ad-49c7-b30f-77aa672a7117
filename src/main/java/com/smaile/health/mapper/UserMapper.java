package com.smaile.health.mapper;

import com.smaile.health.domain.User;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.request.CreateOrgAdminRequest;
import com.smaile.health.model.request.CreateUserRequest;
import com.smaile.health.model.request.UpdateUserRequest;
import com.smaile.health.model.response.UserResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {
                OrganizationMapper.class
        }
)
public interface UserMapper {

    UserDTO toDTO(CreateUserRequest createUserRequest);

    UserDTO toDTO(UpdateUserRequest updateUserRequest);

    UserDTO toDTO(CreateOrgAdminRequest createOrgAdminRequest);

    UserResponse toResponse(UserDTO userDto);

    @Mapping(target = "organizationId",
            expression = "java(user.getOrganization() == null ? null : user.getOrganization().getId())")
    @Mapping(target = "roleCode", expression = "java(UserMapperHelper.getPrimaryRoleCode(user))")
    @Mapping(target = "roleDescription", expression = "java(UserMapperHelper.getPrimaryRoleDescription(user))")
    UserDTO toDTO(User user);

    User toEntity(UserDTO userDTO);

    void updateEntityFromDTO(UserDTO userDTO, @MappingTarget User user);

}