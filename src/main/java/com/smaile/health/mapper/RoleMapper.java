package com.smaile.health.mapper;

import com.smaile.health.domain.Role;
import com.smaile.health.model.RoleDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RoleMapper {

    RoleDTO toDTO(Role role);

    Role toEntity(RoleDTO roleDTO);

    void updateEntityFromDTO(RoleDTO roleDTO, @MappingTarget Role role);

}