package com.smaile.health.mapper;

import com.smaile.health.model.CreateTPARequestDTO;
import com.smaile.health.model.TPADTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface CreateTPARequestMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "type", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "totalAdmin", ignore = true)
    @Mapping(target = "dateCreated", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastUpdated", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "relatedOrganizations", ignore = true)
    TPADTO toTPADTO(CreateTPARequestDTO createTPARequestDTO);

}
