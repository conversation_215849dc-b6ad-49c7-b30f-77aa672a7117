package com.smaile.health.controller;

import com.smaile.health.model.UserDTO;
import com.smaile.health.service.UserService;
import com.smaile.health.util.PageResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/about-me", produces = MediaType.APPLICATION_JSON_VALUE)
public class AboutMeController {

    @GetMapping("")
    public ResponseEntity<PageResponse<UserDTO>> me(@RequestHeader Map<String, String> headers) {
        System.out.println("Headers: " + headers);
        // TODO: Implement logic to retrieve the authenticated user's information
        return null;
    }

}
