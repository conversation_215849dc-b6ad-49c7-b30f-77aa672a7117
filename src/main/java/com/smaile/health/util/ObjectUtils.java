package com.smaile.health.util;

import com.smaile.health.exception.SmaileRuntimeException;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.InvocationTargetException;

public class ObjectUtils {

    private ObjectUtils() {
    }

    public static <T> T clone(Object source, Class<T> destination) {
        if (source == null) {
            return null;
        }
        T result = null;
        try {
            result = destination.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new SmaileRuntimeException("Error when clone object");
        }
        BeanUtils.copyProperties(source, result);
        return result;
    }

}
