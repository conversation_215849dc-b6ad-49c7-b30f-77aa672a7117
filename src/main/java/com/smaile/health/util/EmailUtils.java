package com.smaile.health.util;

import com.smaile.health.model.request.EmailRequest;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.EmailTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
@RequiredArgsConstructor
@Log4j2
public class EmailUtils {

    private final EmailService emailService;
    private final EmailTemplateService templateService;

    /**
     * Send a simple text email
     */
    public void sendSimpleEmail(List<String> to, String subject, String content) {
        EmailRequest emailRequest = EmailRequest.builder()
                .to(to)
                .subject(subject)
                .content(content)
                .contentType("text/plain")
                .build();

        emailService.sendEmail(emailRequest);
    }

    /**
     * Send a simple HTML email
     */
    public void sendHtmlEmail(List<String> to, String subject, String htmlContent) {
        EmailRequest emailRequest = EmailRequest.builder()
                .to(to)
                .subject(subject)
                .content(htmlContent)
                .contentType("text/html")
                .build();

        emailService.sendEmail(emailRequest);
    }

    /**
     * Send a templated email
     */
    public void sendTemplatedEmail(String templateId, Map<String, Object> data, List<String> to, String subject) {
        if (!templateService.templateExists(templateId)) {
            log.error("Template not found: {}", templateId);
            return;
        }

        String content = templateService.processTemplate(templateId, data);
        sendHtmlEmail(to, subject, content);
    }

    /**
     * Send professional registration confirmation email
     */
    public void sendProfessionalRegistrationConfirmationEmail(String email,
                                                              String firstName,
                                                              String lastName,
                                                              String supportEmail,
                                                              String supportPhone) {
        Map<String, Object> data = Map.of(
                "firstName", firstName,
                "lastName", lastName,
                "supportEmail", supportEmail,
                "supportPhone", supportPhone
        );

        sendTemplatedEmail("professional-registration-confirmation", data, List.of(email),
                "Your Registration Has Been Submitted");
    }

    /**
     * Send professional approval notification email
     */
    public void sendProfessionalApprovalEmail(String email,
                                              String firstName,
                                              String lastName,
                                              String username,
                                              String password,
                                              String status,
                                              String rejectionReason) {
        Map<String, Object> data = Map.of(
                "firstName", firstName,
                "lastName", lastName,
                "status", status,
                "rejectionReason", rejectionReason != null ? rejectionReason : ""
        );

        // For now, we'll send a simple text email since we don't have the approval template
        String subject = "Professional Account " + status;
        String content = String.format(
                "<html><body>" +
                        "<h2>Professional Account Status Update</h2>" +
                        "<p>Hello %s %s,</p>" +
                        "<p>Your professional account has been <strong>%s</strong>.</p>" +
                        "%s" +
                        "Account: %s" +
                        "<p>Best regards,<br>The SMAILE Team</p>" +
                        "</body></html>",
                firstName, lastName, status,
                "rejected".equalsIgnoreCase(status) && rejectionReason != null ?
                        "<p><strong>Reason:</strong> " + rejectionReason + "</p>" : "",
                "%s / %s".formatted(username, password)
        );

        sendHtmlEmail(List.of(email), subject, content);
    }

    /**
     * Send email asynchronously
     */
    public CompletableFuture<Void> sendEmailAsync(EmailRequest emailRequest) {
        return emailService.sendEmailAsync(emailRequest)
                .thenAccept(response -> {
                    if (response.isSuccess()) {
                        log.info("Email sent successfully to: {}", response.getRecipients());
                    } else {
                        log.error("Failed to send email: {}", response.getErrorMessage());
                    }
                });
    }

    /**
     * Send bulk emails asynchronously
     */
    public CompletableFuture<Void> sendBulkEmailsAsync(List<EmailRequest> emailRequests) {
        List<CompletableFuture<com.smaile.health.model.EmailResponse>> futures =
                emailService.sendBulkEmailsAsync(emailRequests);

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenAccept(v -> {
                    log.info("Bulk email operation completed for {} emails", emailRequests.size());
                });
    }

}
