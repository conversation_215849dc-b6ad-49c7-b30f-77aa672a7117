package com.smaile.health.util;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public final class MapUtils {

    private MapUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Converts a Multimap to a Map with List values.
     * Creates a new independent Map - changes won't affect the original Multimap.
     */
    public static <K, V> Map<K, List<V>> multimapToMap(Multimap<K, V> multimap) {
        return multimap.asMap()
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> new ArrayList<>(entry.getValue())
                ));
    }

    /**
     * Generic method to build a Multimap from a collection using key and value extractors.
     */
    public static <T, K, V> Multimap<K, V> buildMultimap(
            Collection<T> items,
            Function<T, K> keyExtractor,
            Function<T, Collection<V>> valueExtractor) {

        return items.stream()
                .collect(
                        ArrayListMultimap::create,
                        (map, item) -> {
                            K key = keyExtractor.apply(item);
                            Collection<V> values = valueExtractor.apply(item);
                            values.forEach(value -> map.put(key, value));
                        },
                        Multimap::putAll
                );
    }

    /**
     * Builds a Multimap and immediately converts it to Map<K, List<V>>.
     */
    public static <T, K, V> Map<K, List<V>> buildMapFromCollection(
            Collection<T> items,
            Function<T, K> keyExtractor,
            Function<T, Collection<V>> valueExtractor) {

        Multimap<K, V> multimap = buildMultimap(items, keyExtractor, valueExtractor);
        return multimapToMap(multimap);
    }

    /**
     * Direct stream-based approach without using Multimap (more efficient).
     */
    public static <T, K, V> Map<K, List<V>> buildMapDirectly(
            Collection<T> items,
            Function<T, K> keyExtractor,
            Function<T, Collection<V>> valueExtractor) {

        return items.stream()
                .flatMap(item -> {
                    K key = keyExtractor.apply(item);
                    return valueExtractor.apply(item).stream()
                            .map(value -> new AbstractMap.SimpleEntry<>(key, value));
                })
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
    }

}