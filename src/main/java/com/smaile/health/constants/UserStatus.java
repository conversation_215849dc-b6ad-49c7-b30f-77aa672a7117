package com.smaile.health.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/8/2025
 **/
public enum UserStatus {

    ACTIVE,
    INACTIVE,
    DELETED,
    SUSPENDED;

    private static final Map<String, UserStatus> mappings = new HashMap<>();

    static {
        for (UserStatus status : UserStatus.values()) {
            mappings.put(status.name(), status);
        }
    }

    public static UserStatus resolve(String status) {
        if (status == null) {
            return null;
        }
        return mappings.getOrDefault(status, null);
    }

    public boolean matches(String status) {
        return (this == resolve(status));
    }
}
