package com.smaile.health.constants;

public enum ErrorCode {
    // Insurance Company errors
    INSURANCE_COMPANY_NOT_FOUND("error.insurance-company.not-found"),
    INSURANCE_COMPANY_NAME_DUPLICATE("error.insurance-company.name-duplicate"),

    // TPA errors
    TPA_NOT_FOUND("error.tpa.not-found"),
    TPA_NAME_DUPLICATE("error.tpa.name-duplicate"),
    INSUFFICIENT_PERMISSIONS_TO_CREATE_TPA("error.tpa.insufficient-permissions-to-create"),

    // Organization errors
    ORGANIZATION_NOT_FOUND("error.organization.not-found"),
    ORGANIZATION_NAME_DUPLICATE("error.organization.name-duplicate"),
    USER_ORGANIZATION_NOT_FOUND("error.user-organization.not-found"),

    // User errors
    USER_NOT_FOUND("error.user.not-found"),
    USER_IS_DELETED("error.user.is-deleted"),
    USER_EMAIL_DUPLICATE("error.user.email-duplicate"),

    // Role errors
    ROLE_NOT_FOUND("error.role.not-found"),

    // General errors
    VALIDATION_ERROR("error.validation"),
    INTERNAL_SERVER_ERROR("error.internal-server"),
    PERMISSION_DENIED("error.permission-denied");

    private final String messageKey;

    ErrorCode(String messageKey) {
        this.messageKey = messageKey;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
