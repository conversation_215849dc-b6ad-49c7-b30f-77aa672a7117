package com.smaile.health.constants;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;

public class SmaileConstant {

    public static final String ADMIN = "ADMIN";
    public static final String AUTHORIZATION = "Authorization";
    public static final String BEARER_VALUE = "Bearer %s";
    public static final String ACTIVE = "ACTIVE";
    public static final OffsetDateTime DEFAULT_START_TIME =
            OffsetDateTime.of(1970, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC);
    public static final OffsetDateTime DEFAULT_END_TIME =
            OffsetDateTime.of(9999, 12, 31, 23, 59, 59, 0, ZoneOffset.UTC);
    // https://www.baeldung.com/java-email-validation-regex OWASP
    public static final String EMAIL_REGEX = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    public static final List<OrganizationType> MP_CODE_LIST = List.of(
            OrganizationType.IC_MP,
            OrganizationType.IC_TPA_MP,
            OrganizationType.SMAILE_MP,
            OrganizationType.SMAILE_TPA_MP
    );

    private SmaileConstant() {
    }

}
