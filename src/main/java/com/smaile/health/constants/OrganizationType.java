package com.smaile.health.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum OrganizationType {
    SUPER_SMAILE("SMAILE", "Super Smaile"),
    IC("IC", "Insurance Company"),
    IC_TPA("IC_TPA", "Insurance Company Third-Party Admin"),
    IC_MP("IC_MP", "Insurance Company Medical Provider"),
    IC_TPA_MP("IC_TPA_MP", "Insurance Company Third-Party Admin Medical Provider"),
    SMAILE_TPA("SMAILE_TPA", "Smaile Third-Party Admin"),
    SMAILE_MP("SMAILE_<PERSON>", "Smaile Medical Provider"),
    SMAILE_TPA_MP("SMAILE_TPA_MP", "Smaile Third-Party Admin Medical Provider"),
    PROFESSIONAL("PROFESSIONAL", "Professional");

    private static final Map<String, OrganizationType> mappings = new HashMap<>();

    static {
        for (OrganizationType type : values()) {
            mappings.put(type.code, type);
        }
    }

    private final String code;
    private final String description;

    public static OrganizationType resolve(String type) {
        if (type == null) {
            return null;
        }
        return mappings.get(type);
    }

    public boolean matches(String type) {
        return (this == resolve(type));
    }

}
