package com.smaile.health.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents the active status of an entity.
 * Just active/inactive for now. If needed, you have to use your own
 *
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 26/8/2025
 **/
public enum Status {

    ACTIVE,
    INACTIVE;

    private static final Map<String, Status> mappings = new HashMap<>();

    static {
        for (Status status : Status.values()) {
            mappings.put(status.name(), status);
        }
    }

    public static Status resolve(String status) {
        if (status == null) {
            return null;
        }
        return mappings.getOrDefault(status, null);
    }

    public boolean matches(String status) {
        return (this == resolve(status));
    }

}
