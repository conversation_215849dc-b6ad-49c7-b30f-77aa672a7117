package com.smaile.health.constants;

public enum OrganizationStatus {

    ACTIVE("ACTIVE"),
    INACTIVE("INACTIVE"),
    PENDING("PENDING"),
    REJECTED("REJECTED"),
    DRAFT("DRAFT"), // TODO: to be removed
    ARCHIVED("ARCHIVED"); // TODO: to be removed

    private final String value;

    OrganizationStatus(String value) {
        this.value = value;
    }

    public static OrganizationStatus fromString(String text) {
        for (OrganizationStatus status : OrganizationStatus.values()) {
            if (status.value.equalsIgnoreCase(text)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No constant with text " + text + " found");
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return this.value;
    }
}
