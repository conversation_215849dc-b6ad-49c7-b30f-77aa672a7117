package com.smaile.health.repository;

import com.smaile.health.domain.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface RoleRepository extends JpaRepository<Role, UUID> {

    Optional<Role> findOneByCode(String roleCode);

    @Query("""
            SELECT r
            FROM Role r join fetch r.permissions
            WHERE r.status = 'ACTIVE'
            """)
    List<Role> findAll();

}
