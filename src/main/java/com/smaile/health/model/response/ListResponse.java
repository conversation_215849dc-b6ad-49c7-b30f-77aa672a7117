package com.smaile.health.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ListResponse<T> {

    private int code;
    private String message;
    private List<T> data;

    public ListResponse(List<T> data) {
        this.data = data;
    }

    public static <T> ListResponse<T> of(List<T> data) {
        return new ListResponse<>(0, "success", data);
    }

}
