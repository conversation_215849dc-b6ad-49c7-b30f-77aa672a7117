package com.smaile.health.model.response;

import com.smaile.health.constants.OrganizationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserOrganizationResponse {

    private final String country = "N/A"; //TODO: clarify with Doan
    private final String market = "N/A"; //TODO: clarify with Doan
    private UUID parentId;
    private UUID id;
    private String name;
    private OrganizationType type;
    private String registrationNumber;

}
