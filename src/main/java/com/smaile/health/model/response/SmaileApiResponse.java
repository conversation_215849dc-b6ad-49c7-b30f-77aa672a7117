package com.smaile.health.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Standard API response wrapper")
public class SmaileApiResponse<T> {

    @Schema(
            description = "Response data payload",
            example = "{}"
    )
    private T data;

    @Schema(
            description = "Response message indicating success or error",
            example = "Operation completed successfully"
    )
    private String msg;

    public static <T> SmaileApiResponse<T> success(T data) {
        return new SmaileApiResponse<>(data, "Success");
    }

    public static <T> SmaileApiResponse<T> success(T data, String message) {
        return new SmaileApiResponse<>(data, message);
    }

    public static <T> SmaileApiResponse<T> error(String message) {
        return new SmaileApiResponse<>(null, message);
    }

    public static <T> SmaileApiResponse<T> error(T data, String message) {
        return new SmaileApiResponse<>(data, message);
    }

}
