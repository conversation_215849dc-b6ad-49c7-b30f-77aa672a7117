package com.smaile.health.model.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FindUserRequest {

    private UUID organizationId;
    private List<UUID> userIdList;
    private String partialEmailOrName;
    private String roleCode;
    private String status;

}
