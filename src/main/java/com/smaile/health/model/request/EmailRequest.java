package com.smaile.health.model.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailRequest {

    @NotEmpty(message = "Recipients are required")
    private List<@Email(message = "Invalid email address") String> to;

    private List<@Email(message = "Invalid email address") String> cc;

    private List<@Email(message = "Invalid email address") String> bcc;

    @NotBlank(message = "Subject is required")
    private String subject;

    @NotBlank(message = "Content is required")
    private String content;

    private String contentType = "text/html";

    private String templateId;

    private Map<String, Object> templateData;

    private List<EmailAttachment> attachments;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmailAttachment {

        private String fileName;
        private String contentType;
        private byte[] content;
        private String base64Content;

    }

}
