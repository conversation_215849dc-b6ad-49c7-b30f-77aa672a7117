package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "Admin user data for insurance company")
public class AdminDTO {

    @Schema(
            description = "Full name of the admin user",
            example = "John Doe",
            required = true,
            maxLength = 255
    )
    @NotBlank(message = "Full name is required")
    @Size(max = 255, message = "Full name must not exceed 255 characters")
    @JsonProperty("full_name")
    private String fullName;

    @Schema(
            description = "Email address of the admin user",
            example = "<EMAIL>",
            required = true,
            maxLength = 255
    )
    @NotBlank(message = "Email is required")
    @Email(message = "Email must be a valid email address")
    @Size(max = 255, message = "Email must not exceed 255 characters")
    @JsonProperty("email")
    private String email;

    @Schema(
            description = "Phone number of the admin user",
            example = "******-0123",
            maxLength = 20
    )
    @Size(max = 20, message = "Phone must not exceed 20 characters")
    @JsonProperty("phone")
    private String phone;

    @Schema(
            description = "Username of the admin user",
            example = "johndoe",
            maxLength = 100
    )
    @Size(max = 100, message = "Username must not exceed 100 characters")
    @JsonProperty("username")
    private String username;

}
