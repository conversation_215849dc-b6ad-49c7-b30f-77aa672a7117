package com.smaile.health.model;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MedicalProviderDTO {

    private UUID id;
    private UUID parentId;
    private String name;
    private String code;
    private OrganizationType type;
    private String providerType;
    private String market;
    private String country;
    private String registrationNumber;
    private OrganizationStatus status;
    private String contactPhone;
    private String contactEmail;
    private String address;
    private List<String> specialities;
    private List<MedicalProviderLicense> licenses;

}
