package com.smaile.health.model;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.RoleEnum;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.OffsetDateTime;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationDTO {

    private UUID parentId;

    private UUID id;

    @NotNull
    @Size(max = 255)
    private String name;

    @NotNull
    @Size(max = 100)
    private String type;

    @NotNull
    @Size(max = 100)
    private String code;

    private String country;

    private String market;

    private String registrationNumber;

    private OrganizationStatus status;

    private String contactPhone;

    private String contactEmail;

    private String address;

    private Set<RoleEnum> orgRoleList;

    private OffsetDateTime dateCreated;

    private String createdBy;

    private OffsetDateTime lastUpdated;

    private String updatedBy;

}
