package com.smaile.health.service.impl;

import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.blob.models.BlobProperties;
import com.azure.storage.blob.sas.BlobSasPermission;
import com.azure.storage.blob.sas.BlobServiceSasSignatureValues;
import com.smaile.health.service.ObjectStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

@Service
@Slf4j
public class AzureObjectStorageServiceImpl implements ObjectStorageService {

    private final BlobServiceClient blobServiceClient;
    private final String containerName;

    public AzureObjectStorageServiceImpl(
            @Value("${file-storage.azure.account-name}") String accountName,
            @Value("${file-storage.azure.account-key}") String accountKey,
            @Value("${file-storage.azure.container-name}") String containerName) {

        String connectionString = String.format(
                "DefaultEndpointsProtocol=https;AccountName=%s;AccountKey=%s;EndpointSuffix=core.windows.net",
                accountName, accountKey
        );

        this.blobServiceClient = new BlobServiceClientBuilder()
                .connectionString(connectionString)
                .buildClient();

        this.containerName = containerName;
    }

    @Override
    public void rename(String fromObjectName, String toObjectName) {
        BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(containerName);
        BlobClient sourceBlob = containerClient.getBlobClient(fromObjectName);
        BlobClient targetBlob = containerClient.getBlobClient(toObjectName);
        if (!sourceBlob.exists()) {
            throw new RuntimeException("Source blob does not exist: " + fromObjectName);
        }
        // Copy to new blob
        targetBlob.beginCopy(sourceBlob.getBlobUrl(), Duration.ofSeconds(1));
        // Optionally wait until copy finishes
        BlobProperties properties = targetBlob.getProperties();
        log.debug("Copied blob ETag: {}", properties.getETag());
        // Delete old blob
        sourceBlob.delete();
    }

    @Override
    public String generateUploadPresignedUrl(String objectName) {
        BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(containerName);
        BlobClient blobClient = containerClient.getBlobClient(objectName);

        // Expiration
        OffsetDateTime expiryTime = OffsetDateTime.now(ZoneOffset.UTC).plusMinutes(15);

        // Permissions
        BlobSasPermission permission = new BlobSasPermission();
        permission.setCreatePermission(true);
        permission.setWritePermission(true);
        // Build SAS
        BlobServiceSasSignatureValues sasValues = new BlobServiceSasSignatureValues(expiryTime, permission)
                .setStartTime(OffsetDateTime.now(ZoneOffset.UTC));

        String sasToken = blobClient.generateSas(sasValues);

        return blobClient.getBlobUrl() + "?" + sasToken;
    }

    @Override
    public String generateUploadPresignedUrl(String objectName, OffsetDateTime expiryTime) {
        BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(containerName);
        BlobClient blobClient = containerClient.getBlobClient(objectName);

        // Permissions
        BlobSasPermission permission = new BlobSasPermission();
        permission.setCreatePermission(true);
        permission.setWritePermission(true);
        // Build SAS
        BlobServiceSasSignatureValues sasValues = new BlobServiceSasSignatureValues(expiryTime, permission)
                .setStartTime(OffsetDateTime.now(ZoneOffset.UTC));

        String sasToken = blobClient.generateSas(sasValues);

        return blobClient.getBlobUrl() + "?" + sasToken;
    }

    @Override
    public String generateDownloadUrl(String objectName) {
        BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(containerName);
        BlobClient blobClient = containerClient.getBlobClient(objectName);

        // Expiration
        OffsetDateTime expiryTime = OffsetDateTime.now(ZoneOffset.UTC).plusMinutes(15);

        // Permissions
        BlobSasPermission permission = new BlobSasPermission();
        permission.setReadPermission(true);
        // Build SAS
        BlobServiceSasSignatureValues sasValues = new BlobServiceSasSignatureValues(expiryTime, permission)
                .setStartTime(OffsetDateTime.now(ZoneOffset.UTC));

        String sasToken = blobClient.generateSas(sasValues);

        return blobClient.getBlobUrl() + "?" + sasToken;
    }

    @Override
    public String generateDownloadUrl(String objectName, OffsetDateTime expiryTime) {
        BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(containerName);
        BlobClient blobClient = containerClient.getBlobClient(objectName);

        // Permissions
        BlobSasPermission permission = new BlobSasPermission();
        permission.setReadPermission(true);
        // Build SAS
        BlobServiceSasSignatureValues sasValues = new BlobServiceSasSignatureValues(expiryTime, permission)
                .setStartTime(OffsetDateTime.now(ZoneOffset.UTC));

        String sasToken = blobClient.generateSas(sasValues);

        return blobClient.getBlobUrl() + "?" + sasToken;
    }

    @Override
    public boolean verifyUploaded(String objectName) {
        BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(containerName);
        BlobClient blobClient = containerClient.getBlobClient(objectName);
        return blobClient.exists();
    }

}
