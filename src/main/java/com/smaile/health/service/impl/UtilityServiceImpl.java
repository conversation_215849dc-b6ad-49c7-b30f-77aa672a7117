package com.smaile.health.service.impl;

import com.smaile.health.model.CountryDTO;
import com.smaile.health.model.MarketDTO;
import com.smaile.health.model.MarketSegmentDTO;
import com.smaile.health.model.ProfessionalSpecialtyDTO;
import com.smaile.health.repository.CountryRepository;
import com.smaile.health.repository.MarketRepository;
import com.smaile.health.repository.MarketSegmentRepository;
import com.smaile.health.repository.ProfessionalSpecialtiesRepository;
import com.smaile.health.service.UtilityService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class UtilityServiceImpl implements UtilityService {

    private final MarketRepository marketRepository;
    private final MarketSegmentRepository marketSegmentRepository;
    private final ProfessionalSpecialtiesRepository professionalSpecialtiesRepository;
    private final CountryRepository countryRepository;

    @Override
    public List<MarketDTO> getAllMarket() {
        return marketRepository.findAll().stream()
                .map(entity -> MarketDTO.builder()
                        .id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<CountryDTO> getAllCountry() {
        return countryRepository.findAll().stream()
                .map(entity -> CountryDTO.builder().id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<MarketSegmentDTO> getAllMarketSegment() {
        return marketSegmentRepository.findAll().stream()
                .map(entity -> MarketSegmentDTO.builder()
                        .id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<ProfessionalSpecialtyDTO> getAllProfessionalSpecialty() {
        return professionalSpecialtiesRepository.findAll().stream()
                .map(entity -> ProfessionalSpecialtyDTO.builder()
                        .id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

}
