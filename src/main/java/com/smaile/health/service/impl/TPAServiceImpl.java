package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.TPA;
import com.smaile.health.domain.User;
import com.smaile.health.exception.InternalServerException;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.CreateTPARequestMapper;
import com.smaile.health.mapper.TPAMapper;
import com.smaile.health.model.CreateTPARequestDTO;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.TPADTO;
import com.smaile.health.model.UserDTO;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.TPARepository;
import com.smaile.health.repository.specification.TPASpecification;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.TPAService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.RoleBasedFilteringUtil;
import com.smaile.health.util.UUIDv7;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class TPAServiceImpl implements TPAService {

    private final TPARepository tpaRepository;
    private final TPAMapper tpaMapper;
    private final CreateTPARequestMapper createRequestMapper;
    private final I18nService i18nService;
    private final OrganizationRepository organizationRepository;
    private final UserService userService;
    private final EmailService emailService;

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize(
            "@permissionEvaluator.hasPermission(authentication, T(com.smaile.health.util.SecurityUtils).getCurrentUserOrganizationId().orElse(null), 'organizations:*:read')")
    public Page<TPADTO> search(String country,
                               String status,
                               String name,
                               String registrationNumber,
                               String adminType,
                               String icId,
                               Pageable pageable) {
        long startTime = System.currentTimeMillis();
        log.debug(
                "Starting TPA search operation with filters - Country: {}, Status: {}, Name: {}, Registration Number: {}, Admin Type: {}, IC ID: {}, Page: {}",
                country, status, name, registrationNumber, adminType, icId, pageable.getPageNumber());

        try {
            // Get current user's roles for role-based filtering
            Set<String> currentUserRoles = new HashSet<>(); //SecurityUtils.getActorContext().getRoles();
            OrganizationType tpaTypeFilter = RoleBasedFilteringUtil.determineTPATypeFilterFromRoles(currentUserRoles);

            // Get current user's organization ID for organization-based filtering
            UUID currentUserOrgId = null;
            if (!RoleBasedFilteringUtil.shouldSeeAllOrganizations(currentUserRoles)) {
                currentUserOrgId = SecurityUtils.getCurrentUserOrganizationId().orElseThrow(
                        () -> new ValidationException(
                                i18nService.getMessage(ErrorCode.USER_ORGANIZATION_NOT_FOUND.getMessageKey())));
            }

            log.debug("Applying role-based TPA type filter: {} and organization filter: currentUserOrgId={}",
                    tpaTypeFilter, currentUserOrgId);

            Specification<TPA> spec = TPASpecification.searchSpecification(country, status, name, registrationNumber,
                    icId, tpaTypeFilter, adminType, currentUserOrgId);
            Page<TPA> tpas = tpaRepository.findAll(spec, pageable);

            long endTime = System.currentTimeMillis();
            log.debug(
                    "TPA search operation completed in {} ms, found {} results with role-based filtering for TPA type: {} and organization filter: {}",
                    endTime - startTime, tpas.getTotalElements(), tpaTypeFilter, currentUserOrgId);

            return tpas.map(this::mapToDTOWithRelatedOrgs);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("TPA search operation failed after {} ms with error: {}", endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:read')")
    public TPADTO get(UUID id) {
        log.debug("Fetching TPA with ID: {}", id);

        try {
            TPA tpa = tpaRepository.findByIdAndStatusNot(id, OrganizationStatus.ARCHIVED).orElseThrow(
                    () -> new NotFoundException(i18nService.getMessage(ErrorCode.TPA_NOT_FOUND.getMessageKey())));

            // Check if user has access to this TPA based on their role
            Set<String> currentUserRoles = new HashSet<>(); //SecurityUtils.getActorContext().getRoles();
            if (!RoleBasedFilteringUtil.hasAccessToTPAType(tpa.getType(), currentUserRoles)) {
                log.warn("User with roles {} attempted to access TPA {} with type {} but was denied", currentUserRoles,
                        id, tpa.getType());
                throw new ValidationException(i18nService.getMessage(ErrorCode.PERMISSION_DENIED.getMessageKey()));
            }

            return mapToDTOWithRelatedOrgs(tpa);
        } catch (NotFoundException | ValidationException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to retrieve TPA with ID {}: {}", id, e.getMessage(), e);
            throw new InternalServerException(i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e);
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize(
            "@permissionEvaluator.hasPermission(authentication, T(com.smaile.health.util.SecurityUtils).getCurrentUserOrganizationId().orElse(null), 'organizations:*:create')")
    public UUID create(CreateTPARequestDTO requestDTO) {
        log.debug("Creating new TPA: {}", requestDTO.getName());
        long startTime = System.currentTimeMillis();

        try {
            // Validate name duplication (excluding archived)
            if (tpaRepository.findByNameAndStatusNot(requestDTO.getName(), OrganizationStatus.ARCHIVED).isPresent()) {
                log.warn("Attempted to create TPA with duplicate name: {}", requestDTO.getName());
                throw new ValidationException(i18nService.getMessage(ErrorCode.TPA_NAME_DUPLICATE.getMessageKey()));
            }

            // Admin user information is now required and validated at DTO level
            log.debug("Will create new admin user: {} after TPA creation", requestDTO.getAdmin().getEmail());

            // Get current user's organization to set as parent and determine TPA type
            UUID currentUserOrgId = SecurityUtils.getCurrentUserOrganizationId().orElseThrow(
                    () -> new ValidationException(
                            i18nService.getMessage(ErrorCode.USER_ORGANIZATION_NOT_FOUND.getMessageKey())));

            Organization parentOrg = organizationRepository.findById(currentUserOrgId).orElseThrow(
                    () -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.ORGANIZATION_NOT_FOUND.getMessageKey())));

            // Determine TPA type based on current user's organization type
            OrganizationType tpaType = determineTPAType(parentOrg);

            // Convert request DTO to TPA DTO
            TPADTO tpaDTO = createRequestMapper.toTPADTO(requestDTO);

            TPA tpa = tpaMapper.toEntity(tpaDTO);
            tpa.setId(UUIDv7.generate());
            tpa.setType(tpaType);
            tpa.setStatus(OrganizationStatus.ACTIVE);
            tpa.setParent(parentOrg);

            log.debug("Saving TPA entity with ID: {} and type: {}", tpa.getId(), tpa.getType());
            TPA savedTPA = tpaRepository.save(tpa);

            // Create admin user (now required)
            UserDTO userDTO = new UserDTO();
            userDTO.setEmail(requestDTO.getAdmin().getEmail());
            userDTO.setUsername(requestDTO.getAdmin().getUsername());
            userDTO.setFullName(requestDTO.getAdmin().getFullName());
            userDTO.setPhone(requestDTO.getAdmin().getPhone());
            userDTO.setRoleCode(determineAdminRoleCode(tpaType));
            userDTO.setStatus(Status.ACTIVE);
            SmaileUserCredential userCredential = userService.createWithPasswordGenerated(savedTPA.getId(), userDTO,
                    false, true);

            emailService.sendOrganizationOnboardNotification(userDTO, savedTPA, userCredential);

            long endTime = System.currentTimeMillis();
            log.info("Successfully created TPA with admin: {} (ID: {}, Type: {}) in {} ms", savedTPA.getName(),
                    savedTPA.getId(), savedTPA.getType(), endTime - startTime);

            return savedTPA.getId();
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to create TPA after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e);
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:update')")
    public void update(UUID id, TPADTO tpaDTO) {
        log.debug("Updating TPA with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            TPA existingTPA = tpaRepository.findByIdAndStatusNot(id, OrganizationStatus.ARCHIVED).orElseThrow(
                    () -> new NotFoundException(i18nService.getMessage(ErrorCode.TPA_NOT_FOUND.getMessageKey())));

            if (existingTPA.getStatus() == OrganizationStatus.ARCHIVED) {
                log.warn("Attempted to update archived TPA with ID: {}", id);
                throw new ValidationException(i18nService.getMessage(MessageKey.TPA_CANNOT_UPDATE_ARCHIVED.getKey()));
            }

            // Validate name duplication (excluding current entity and archived)
            if (tpaRepository.findByNameAndIdNot(tpaDTO.getName(), id).isPresent()) {
                log.warn("Attempted to update TPA with duplicate name: {}", tpaDTO.getName());
                throw new ValidationException(i18nService.getMessage(ErrorCode.TPA_NAME_DUPLICATE.getMessageKey()));
            }
            log.debug("Updating TPA entity: {} -> {}", existingTPA.getName(), tpaDTO.getName());

            tpaDTO.setId(id);
            // Preserve the original TPA type
            tpaDTO.setType(existingTPA.getType());
            tpaMapper.updateEntityFromDTO(tpaDTO, existingTPA);
            tpaRepository.save(existingTPA);

            long endTime = System.currentTimeMillis();
            log.info("Successfully updated TPA: {} (ID: {}) in {} ms", existingTPA.getName(), existingTPA.getId(),
                    endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to update TPA with ID {} after {} ms: {}", id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e);
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:delete')")
    public void delete(UUID id) {
        log.debug("Archiving TPA with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            TPA tpa = tpaRepository.findByIdAndStatusNot(id, OrganizationStatus.ARCHIVED).orElseThrow(
                    () -> new NotFoundException(i18nService.getMessage(ErrorCode.TPA_NOT_FOUND.getMessageKey())));

            if (tpa.getStatus() == OrganizationStatus.ARCHIVED) {
                log.warn("Attempted to archive already archived TPA with ID: {}", id);
                throw new ValidationException(i18nService.getMessage(MessageKey.TPA_ALREADY_ARCHIVED.getKey()));
            }

            log.debug("Archiving TPA: {} (Current Status: {})", tpa.getName(), tpa.getStatus());

            tpa.setStatus(OrganizationStatus.ARCHIVED);
            tpaRepository.save(tpa);

            long endTime = System.currentTimeMillis();
            log.info("Successfully archived TPA: {} (ID: {}) in {} ms", tpa.getName(), tpa.getId(),
                    endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to archive TPA with ID {} after {} ms: {}", id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e);
        }
    }

    /**
     * Maps TPA to DTO with total admin count and related organizations
     */
    private TPADTO mapToDTOWithRelatedOrgs(TPA tpa) {
        TPADTO dto = tpaMapper.toDTO(tpa);
        dto.setTotalAdmin(tpa.getUserOrganizations().size());

        // Ensure related organizations are loaded and filtered
        if (tpa.getLinkedOrganizations() != null) {
            List<OrganizationDTO> relatedOrgs = tpa.getLinkedOrganizations().stream()
                    .filter(org -> org.getStatus() != OrganizationStatus.ARCHIVED).map(org -> {
                        OrganizationDTO orgDTO = new OrganizationDTO();
                        orgDTO.setId(org.getId());
                        orgDTO.setName(org.getName());
                        orgDTO.setCode(org.getCode());
                        orgDTO.setType(org.getType().toString());
                        orgDTO.setStatus(org.getStatus());
                        return orgDTO;
                    }).toList();
            dto.setRelatedOrganizations(relatedOrgs);
        }

        // Check if current user has SUPER_SMAILE_ADMIN role to include user information
        List<UserDTO> users = tpa.getUserOrganizations().stream().map(userOrg -> {
            User user = userOrg.getUser();
            UserDTO userDTO = new UserDTO();

            // Only set the required fields
            userDTO.setEmail(user.getEmail());
            userDTO.setFullName(user.getFullName());
            userDTO.setPhone(user.getPhone());

            // Get role code from UserRole
            String roleCode = userOrg.getUserRoles().stream().findFirst().map(userRole -> userRole.getRole().getCode())
                    .orElse(null);
            userDTO.setRoleCode(roleCode);

            return userDTO;
        }).toList();

        dto.setUsers(users);
        return dto;
    }

    /**
     * Determines the TPA type based on the parent organization type
     *
     * @param parentOrg the parent organization
     * @return the appropriate TPA type
     * @throws ValidationException if the parent organization type is not allowed to create TPAs
     */
    private OrganizationType determineTPAType(Organization parentOrg) {
        return switch (parentOrg.getType()) {
            case IC -> {
                log.debug("Current user is from IC organization, setting TPA type to IC_TPA");
                yield OrganizationType.IC_TPA;
            }
            case SUPER_SMAILE -> {
                log.debug("Current user is from SUPER_SMAILE organization, setting TPA type to SMAILE_TPA");
                yield OrganizationType.SMAILE_TPA;
            }
            default -> {
                log.error("Current user's organization type {} is not allowed to create TPA", parentOrg.getType());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.INSUFFICIENT_PERMISSIONS_TO_CREATE_TPA.getMessageKey()));
            }
        };
    }

    /**
     * Determines the appropriate admin role code based on TPA type
     *
     * @param tpaType the type of TPA
     * @return the role code for the admin user
     */
    private String determineAdminRoleCode(OrganizationType tpaType) {
        return switch (tpaType) {
            case IC_TPA -> RoleEnum.IC_TPA_ADMIN.name();
            case SMAILE_TPA -> RoleEnum.SMAILE_TPA_ADMIN.name();
            default -> {
                log.warn("Unknown TPA type: {}, using default admin role", tpaType);
                yield RoleEnum.IC_TPA_ADMIN.name();
            }
        };
    }

}
