package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.config.AppConfig;
import com.smaile.health.config.EmailConfig;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.constants.Status;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import com.smaile.health.domain.UserOrganization;
import com.smaile.health.domain.UserRole;
import com.smaile.health.domain.projection.UserInfo;
import com.smaile.health.domain.projection.UsersSummaryProjection;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.mapper.UserMapper;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.UsersSummaryDTO;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.repository.UserOrganizationRepository;
import com.smaile.health.repository.UserRepository;
import com.smaile.health.repository.UserRoleRepository;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.IdentityProvider;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.EmailUtils;
import com.smaile.health.util.PasswordUtils;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static com.smaile.health.constants.Status.ACTIVE;

@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final UserOrganizationRepository userOrganizationRepository;
    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;
    private final OrganizationRepository organizationRepository;
    private final IdentityProvider identityProvider;
    private final UserMapper userMapper;
    private final EmailService emailService;
    private final AppConfig appConfig;
    private final OrganizationService organizationService;
    private final EmailUtils emailUtils;
    private final EmailConfig emailConfig;

    @Override
    @PreAuthorize("@smaileAuthorizationService.hasPermissionInOrganization(authentication, #orgId, 'users:*:create')")
    @LogExecution
    @Transactional
    public SmaileUserCredential createWithPasswordGenerated(UUID orgId,
                                                            UserDTO userDTO,
                                                            boolean notification,
                                                            boolean hasTempPassword) {
        Organization organization = organizationRepository.findById(orgId)
                .orElseThrow(() -> new SmaileRuntimeException("Cannot find organization %s".formatted(orgId)));
        String roleCode = userDTO.getRoleCode();
        String orgTypeCode = organization.getType().name();
        if (roleCode.length() <= orgTypeCode.length()
                || !roleCode.startsWith(orgTypeCode)
                || roleCode.substring(orgTypeCode.length() + 1).contains("_")) {
            throw new SmaileRuntimeException("Cannot create user with role = %s for Organization %s [%s]"
                    .formatted(roleCode, organization.getId(), orgTypeCode));
        }

        String password = PasswordUtils.generateTemporaryPassword(8);
        UUID userId = createUserInternal(orgId, userDTO, password, null, notification, hasTempPassword);
        return SmaileUserCredential.builder()
                .id(userId)
                .password(password)
                .build();
    }

    @Override
    public UserDTO getAboutMe() {
        UserInfo userinfo = SecurityContextUtils.getCurrentUserInfo();
        return get(userinfo.getId());
    }

    @Override
    @LogExecution
    public boolean isAvailableToCreate(UserDTO userDto) {
        if (userRepository.findOneByEmail(userDto.getEmail()).isPresent()) {
            return false;
        }
        return identityProvider.findByEmailAndUsername(userDto.getEmail(), userDto.getUsername()).isEmpty();
    }

    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #organizationId, 'users:*:read')")
    @LogExecution
    @Override
    public Page<UserDTO> queryUserByCriteria(UUID organizationId,
                                             String partialNameOrEmail,
                                             RoleEnum role,
                                             Status status,
                                             Pageable pageable) {
        Page<User> userPage = userRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (organizationId != null) {
                Set<UUID> orgIdList = organizationService.getOrganizationWithChildrenIds(organizationId);
                predicates.add(root.get("organization").get("id").in(orgIdList));
            }

            addCommonUserPredicates(predicates, root, cb, partialNameOrEmail, role, status);

            return predicates.stream().reduce(cb::and).orElseGet(cb::conjunction);
        }, pageable);
        Page<UserDTO> pageUserDTO = userPage.map(userMapper::toDTO);
        pageUserDTO.forEach(user -> {
            String username = identityProvider.getUsernameById(user.getKeycloakId());
            user.setUsername(username);
        });

        return pageUserDTO;
    }

    @Override
    @LogExecution
    public UsersSummaryDTO queryUsersSummaryData() {
        UUID actorOrgId = null;
        if (!SecurityContextUtils.hasRole(RoleEnum.SUPER_SMAILE_ADMIN.name())) {
            actorOrgId = SecurityContextUtils.getOwnerOrganizationId();
        }
        UsersSummaryProjection usersSummary = userRepository.getUsersSummary(actorOrgId);
        return UsersSummaryDTO.builder()
                .totalUsers(usersSummary.getTotalUsers())
                .activeUsers(usersSummary.getActiveUsers())
                .inactiveUsers(usersSummary.getInactiveUsers())
                .totalOrganizations(usersSummary.getTotalOrganizations())
                .build();
    }

    public UUID resetPassword(UUID orgId, UserDTO userDTO) {
        String newPassword = PasswordUtils.generateTemporaryPassword(8);
        identityProvider.resetPassword(userDTO.getKeycloakId(), newPassword);

        String subject = "Your Temporary Password for Account Access";
        Map<String, Object> contentData = Map.of(
                "userName", userDTO.getFullName(),
                "temporaryPassword", newPassword,
                "loginUrl", appConfig.getLoginUrl(),
                "supportEmail", emailConfig.getSupport().getEmail(),
                "supportPhone", emailConfig.getSupport().getPhone()
        );
        emailService.sendTemplatedEmail("user-reset-password", contentData, List.of(userDTO.getEmail()), subject);
        return userDTO.getId();
    }

    /**
     * @param scopedOrgId Organization ID where the user will be created
     * @param userDto     User data transfer object
     * @param password    Optional password (for professional users or temporary passwords)
     * @param forcedRole  Optional role override (e.g., for professional users)
     * @return Created user ID
     */
    private UUID createUserInternal(UUID scopedOrgId, final UserDTO userDto, String password,
                                    RoleEnum forcedRole, boolean notification, boolean hasTempPassword) {
        // Validate organization
        Organization organization = validateAndGetOrganization(scopedOrgId, forcedRole);

        // Validate user doesn't already exist
        validateUserDoesNotExist(userDto.getEmail());

        // Get or determine role
        Role role = getOrDetermineRole(userDto, forcedRole);

        if (Strings.CS.endsWith(userDto.getRoleCode(), SmaileConstant.ADMIN) && userDto.getPhone() == null) {
            throw new SmaileRuntimeException(
                    "User with empty phone info cannot be initialized as %s".formatted(userDto.getRoleCode()));
        }

        // Create user in identity provider
        String kcUserId = createUserInIdentityProvider(userDto, password, hasTempPassword);

        // Create user entity and relationships
        User user = createUserEntity(userDto, kcUserId, organization, role);
        createUserRelationships(user, organization, role);

        if (notification) {
            // TODO: consider do this Async
            emailUtils.sendTemplatedEmail("user-creation-notification",
                    Map.of(
                            "userName", user.getFullName(),
                            "roleList", List.of(role.getName()),
                            "organizationName", organization.getName(),
                            "organizationType", organization.getType().getDescription(),
                            "temporaryPassword", Optional.ofNullable(password).orElse(""),
                            "loginUrl", appConfig.getLoginUrl(),
                            "supportEmail", emailConfig.getSupport().getEmail(),
                            "supportPhone", emailConfig.getSupport().getPhone()
                    ), List.of(user.getEmail()), "Welcome to SMAILE");
        }

        return user.getId();
    }

    @Override
    public UUID createProfessionalUser(UUID orgId, UserDTO userDto, String password) {
        Organization organization = organizationRepository.findById(orgId)
                .orElseThrow(
                        () -> new SmaileRuntimeException("No organization found for id [%s]".formatted(orgId)));
        if (!OrganizationType.PROFESSIONAL.equals(organization.getType())) {
            throw new SmaileRuntimeException(
                    "Organization with id [%s] is not a professional organization".formatted(orgId));
        }
        userRepository.findOneByEmail(userDto.getEmail())
                .ifPresent(u -> {
                    throw new SmaileRuntimeException("User with email [%s] existed".formatted(u.getEmail()));
                });
        Role role = roleRepository.findOneByCode(userDto.getRoleCode())
                .orElseThrow(
                        () -> new SmaileRuntimeException(
                                "No role found for code %s".formatted(userDto.getRoleCode())));

        String kcUserId = identityProvider.createUser(userDto);

        Role role = roleRepository.findOneByCode(RoleEnum.PROFESSIONAL.name()).orElseThrow(
                () -> new SmaileRuntimeException(
                        "No role found for code %s".formatted(RoleEnum.PROFESSIONAL.name())));
        String kcUserId = identityProvider.createUserWithPassword(userDto, password, true);
        log.info("Created KC user id {} ", kcUserId);
        User user = User.builder()
                .id(UUIDv7.generate())
                .keycloakId(kcUserId)
                .email(userDto.getEmail())
                .fullName(userDto.getFullName())
                .phone(userDto.getPhone())
                .status(UserStatus.resolve(userDto.getStatus()))
                .status(userDto.getStatus())
                .organization(organization)
                .build();
        userRepository.save(user);

        UserOrganization userOrg = UserOrganization.builder()
                .id(UUIDv7.generate())
                .user(user)
                .organization(organization)
                .status(ACTIVE)
                .build();
        userOrganizationRepository.save(userOrg);

        UserRole userRole = UserRole.builder()
                .id(UUIDv7.generate())
                .userOrganization(userOrg)
                .role(role)
                .startTime(SmaileConstant.DEFAULT_START_TIME)
                .endTime(SmaileConstant.DEFAULT_END_TIME)
                .status(ACTIVE)
                .build();
        userRoleRepository.save(userRole);
        return user.getId();
    }

    @Override
    public void updateUser(UUID id, UserDTO userDto) {
        UUID organizationId = userRepository.findById(id)
                .map(User::getOrganization)
                .map(Organization::getId)
                .orElseThrow(() -> new SmaileRuntimeException(
                        "Cannot find primary organization for user %s".formatted(userDto.getId())));
        update(organizationId, id, userDto);
    }

    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #scopedOrgId, 'users:*:update')")
    void update(final UUID scopedOrgId, final UUID userId, final UserDTO userDto) {
        if (!Objects.equals(userId, userDto.getId())) {
            throw new SmaileRuntimeException(
                    "User id in payload [%s] not match [%s]".formatted(userDto.getId(), userId));
        }

        User existingUser = userRepository.findById(userId)
                .filter(user -> user.getUserOrganizations().stream()
                        .filter(uo -> Objects.equals(uo.getStatus(), SmaileConstant.ACTIVE))
                        .anyMatch(uo -> Objects.equals(uo.getOrganization().getId(), scopedOrgId))
                )
                .orElseThrow(() -> new SmaileRuntimeException(
                        "User with id = %s not found for org %s".formatted(userId, scopedOrgId)));

        existingUser.setFullName(userDto.getFullName());
        existingUser.setStatus(userDto.getStatus());
        existingUser.setPhone(userDto.getPhone());

        userRepository.save(existingUser);
    }

    @Override
    @LogExecution
    public UserDTO get(final UUID id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new SmaileRuntimeException("User with id = %s not found".formatted(id)));
        UserDTO userDTO = userMapper.toDTO(user);
        userDTO.setUsername(identityProvider.getUsernameById(user.getKeycloakId()));

        return userDTO;
    }

    /**
     * Adds common user predicates to the predicate list
     */
    private void addCommonUserPredicates(List<Predicate> predicates, Root<User> root, CriteriaBuilder cb,
                                         String partialNameOrEmail, RoleEnum role, Status status) {
        if (StringUtils.isNotBlank(partialNameOrEmail)) {
            String pattern = "%" + partialNameOrEmail.toLowerCase() + "%";
            predicates.add(cb.or(
                    cb.like(cb.lower(root.get("email")), pattern),
                    cb.like(cb.lower(root.get("fullName")), pattern)
            ));
        }
        if (status != null) {
            predicates.add(cb.equal(root.get("status"), status.name()));
        }
        if (role != null) {
            Join<User, UserOrganization> userOrgJoin = root.join("userOrganizations");
            Join<UserOrganization, UserRole> userRoleJoin = userOrgJoin.join("userRoles");
            Join<UserRole, Role> roleJoin = userRoleJoin.join("role");
            predicates.add(cb.equal(roleJoin.get("code"), role.name()));
        }
    }

    /**
     * Validates organization and returns it, with type validation for professional users
     */
    private Organization validateAndGetOrganization(UUID scopedOrgId, RoleEnum forcedRole) {
        Organization organization = organizationRepository.findById(scopedOrgId)
                .orElseThrow(() -> new SmaileRuntimeException(
                        "No organization found for id [%s]".formatted(scopedOrgId)));

        // Validate organization type for professional users
        if (forcedRole == RoleEnum.PROFESSIONAL && !OrganizationType.PROFESSIONAL.equals(organization.getType())) {
            throw new SmaileRuntimeException(
                    "Organization with id [%s] is not a professional organization".formatted(scopedOrgId));
        }

        return organization;
    }

    /**
     * Validates that user doesn't already exist
     */
    private void validateUserDoesNotExist(String email) {
        userRepository.findOneByEmail(email)
                .ifPresent(u -> {
                    throw new SmaileRuntimeException("User with email [%s] existed".formatted(u.getEmail()));
                });
    }

    /**
     * Gets or determines the role for the user
     */
    private Role getOrDetermineRole(UserDTO userDto, RoleEnum forcedRole) {
        if (forcedRole != null) {
            return roleRepository.findOneByCode(forcedRole.name())
                    .orElseThrow(() -> new SmaileRuntimeException(
                            "No role found for code %s".formatted(forcedRole.name())));
        } else {
            return roleRepository.findOneByCode(userDto.getRoleCode())
                    .orElseThrow(() -> new SmaileRuntimeException(
                            "No role found for code %s".formatted(userDto.getRoleCode())));
        }
    }

    /**
     * Creates user in identity provider
     */
    private String createUserInIdentityProvider(UserDTO userDto, String password, boolean hasTempPassword) {
        if (password != null && !password.isBlank()) {
            return identityProvider.createUserWithPassword(userDto, password, hasTempPassword);
        } else {
            return identityProvider.createUser(userDto);
        }
    }

    /**
     * Creates the user entity
     */
    private User createUserEntity(UserDTO userDto, String kcUserId, Organization organization) {
        User user = User.builder()
                .id(UUIDv7.generate())
                .keycloakId(kcUserId)
                .email(userDto.getEmail())
                .fullName(userDto.getFullName())
                .status(userDto.getStatus())
                .phone(userDto.getPhone())
                .status(userDto.getStatus())
                .organization(organization)
                .build();

        log.debug("Creating db user: {}", user);
        userRepository.save(user);
        log.debug("Smaile user created, smaile user id = {}", user.getId());

        return user;
    }

    /**
     * Creates user-organization and user-role relationships
     */
    private void createUserRelationships(User user, Organization organization, Role role) {
        UserOrganization userOrg = UserOrganization.builder()
                .id(UUIDv7.generate())
                .user(user)
                .organization(organization)
                .status(ACTIVE)
                .build();
        userOrganizationRepository.save(userOrg);

        UserRole userRole = UserRole.builder()
                .id(UUIDv7.generate())
                .userOrganization(userOrg)
                .role(role)
                .startTime(SmaileConstant.DEFAULT_START_TIME)
                .endTime(SmaileConstant.DEFAULT_END_TIME)
                .status(ACTIVE)
                .build();
        userRoleRepository.save(userRole);
    }

}
