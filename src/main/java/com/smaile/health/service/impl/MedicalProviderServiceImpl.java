package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.domain.MedicalProvider;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.model.MedicalProviderDTO;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.repository.MedicalProviderRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.MedicalProviderService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class MedicalProviderServiceImpl implements MedicalProviderService {

    private final MedicalProviderRepository medicalProviderRepository;
    private final MedicalProviderMapper medicalProviderMapper;
    private final OrganizationRepository organizationRepository;
    private final UserService userService;
    private final EmailService emailService;

    @Override
    @LogExecution
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #scopedOrgId, 'organizations:*:create')")
    @Transactional
    public UUID create(UUID scopedOrgId, MedicalProviderDTO dto, UserDTO adminDto) {
        Organization parentOrg = organizationRepository.findById(scopedOrgId)
                .orElseThrow(() -> new SmaileRuntimeException("Org id %s not found".formatted(scopedOrgId)));

        validateMedicalProviderNameNotExist(dto);

        if (!userService.isAvailableToCreate(adminDto)) {
            throw new SmaileRuntimeException(
                    "Admin user email = %s and/or username = %s] existed".formatted(adminDto.getEmail(),
                            adminDto.getUsername()));
        }

        // Create medical provider
        MedicalProvider medicalProviderEntity = medicalProviderMapper.toEntity(dto);
        medicalProviderEntity.setId(UUIDv7.generate());
        medicalProviderEntity.setParent(parentOrg);
        medicalProviderEntity.setType(OrganizationType.valueOf(parentOrg.getType().getCode() + "_MP"));
        medicalProviderRepository.save(medicalProviderEntity);

        String roleCode = Objects.equals(parentOrg.getType(), OrganizationType.SUPER_SMAILE) ?
                "SMAILE_MP_ADMIN" : (parentOrg.getType().name() + "_MP_ADMIN");

        adminDto.setOrganizationId(medicalProviderEntity.getId());
        adminDto.setRoleCode(roleCode);

        SmaileUserCredential createdCredential = userService.createWithPasswordGenerated(medicalProviderEntity.getId(),
                adminDto, false, true);

        emailService.sendOrganizationOnboardNotification(adminDto, medicalProviderEntity, createdCredential);
        return medicalProviderEntity.getId();
    }

    private void validateMedicalProviderNameNotExist(MedicalProviderDTO dto) {
        organizationRepository.findOne((root, query, cb) ->
                cb.and(
                        cb.equal(root.get("name"), dto.getName()),
                        root.get("type").in(SmaileConstant.MP_CODE_LIST)
                )
        ).ifPresent(mp -> {
            throw new SmaileRuntimeException("Provider name already exists");
        });
    }

    @Override
    @LogExecution
    public MedicalProviderDTO getById(UUID id) {
        MedicalProvider medicalProvider = medicalProviderRepository.findOne(
                (root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(cb.equal(root.get("id"), id));
                    predicates.add(cb.isFalse(root.get("isDeleted")));
                    return predicates.stream().reduce(cb::and).orElseGet(cb::conjunction);
                }
        ).orElseThrow(() -> new SmaileRuntimeException("Medical Provider with id = %s not found".formatted(id)));
        return medicalProviderMapper.toDTO(medicalProvider);
    }

    @Override
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #id, 'organizations:*:update')")
    @LogExecution
    public void update(UUID id, MedicalProviderDTO dto) {
        MedicalProvider medicalProviderEntity = medicalProviderRepository.findById(id)
                .orElseThrow(
                        () -> new SmaileRuntimeException("Cannot find Medical provider with id = %s".formatted(id)));
        if (!Objects.equals(dto.getName(), medicalProviderEntity.getName())) {
            validateMedicalProviderNameNotExist(dto);
        }
        log.debug("Medical provider before update: {}", medicalProviderEntity);
        medicalProviderMapper.updateEntity(dto, medicalProviderEntity);
        medicalProviderRepository.save(medicalProviderEntity);
        log.debug("Medical provider after update: {}", medicalProviderEntity);
    }

    @Override
    public void delete(UUID id) {
        MedicalProvider medicalProviderEntity = medicalProviderRepository.findById(id)
                .orElseThrow(
                        () -> new SmaileRuntimeException("Cannot find medical provider with id = %s".formatted(id)));
        deleteWithAuthCheck(medicalProviderEntity);
    }

    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #orgId, 'organizations:*delete')")
    @LogExecution
    private void deleteWithAuthCheck(MedicalProvider entity) {
        entity.setDeleted(true);
        medicalProviderRepository.save(entity);
    }

    @Override
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #scopedOrgId, 'organizations:*:read')")
    public Page<MedicalProviderDTO> query(UUID scopedOrgId,
                                          String keyword,
                                          OrganizationStatus status,
                                          String market,
                                          Pageable pageable) {
        Page<MedicalProvider> medicalProviderPage = medicalProviderRepository.findAll(
                (root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    if (scopedOrgId != null) {
                        Join<MedicalProvider, Organization> parent = root.join("parent", JoinType.LEFT);
                        predicates.add(cb.equal(parent.get("id"), scopedOrgId));
                    }

                    if (StringUtils.isNotBlank(keyword)) {
                        String pattern = "%" + keyword.toLowerCase() + "%";
                        predicates.add(cb.or(
                                        cb.like(cb.lower(root.get("name")), pattern),
                                        cb.like(cb.lower(root.get("code")), pattern)
                                )
                        );
                    }

                    if (status != null) {
                        predicates.add(cb.equal(root.get("status"), status));
                    }

                    if (StringUtils.isNotBlank(market)) {
                        predicates.add(cb.equal(root.get("market"), market));
                    }

                    predicates.add(cb.isFalse(root.get("isDeleted")));

                    return predicates.stream().reduce(cb::and).orElseGet(cb::conjunction);
                }, pageable);
        return medicalProviderPage.map(medicalProviderMapper::toDTO);
    }

}
