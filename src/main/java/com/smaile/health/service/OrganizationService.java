package com.smaile.health.service;

import com.smaile.health.domain.Organization;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.model.response.PageResponse;
import org.springframework.data.domain.Pageable;

import java.util.Set;
import java.util.UUID;

public interface OrganizationService {

    Organization getSmaileOrganization();

    Set<UUID> getOrganizationWithChildrenIds(UUID organizationId);

    PageResponse<OrganizationDTO> getAllOrganizationsPaged(Pageable pageable);

    PageResponse<OrganizationDTO> getCurrentOrgWithChildrenPaged(UUID currentOrgId, Pageable pageable);

}