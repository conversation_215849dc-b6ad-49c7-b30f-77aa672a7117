package com.smaile.health.service;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.model.MedicalProviderDTO;
import com.smaile.health.model.UserDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface MedicalProviderService {

    UUID create(UUID scopedOrgId, MedicalProviderDTO dto, UserDTO adminDto);

    MedicalProviderDTO getById(UUID id);

    Page<MedicalProviderDTO> query(UUID scopedOrgId,
                                   String keyword,
                                   OrganizationStatus status,
                                   String market,
                                   Pageable pageable);

    void update(UUID id, MedicalProviderDTO dto);

    void delete(UUID id);

}
