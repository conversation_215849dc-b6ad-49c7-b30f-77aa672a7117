package com.smaile.health.service;

import java.time.OffsetDateTime;

public interface ObjectStorageService {

    void rename(String fromObjectName, String toObjectName);

    String generateUploadPresignedUrl(String objectName);

    String generateUploadPresignedUrl(String objectName, OffsetDateTime expiryTime);

    String generateDownloadUrl(String objectName);

    String generateDownloadUrl(String objectName, OffsetDateTime expiryTime);

    boolean verifyUploaded(String objectName);

}
