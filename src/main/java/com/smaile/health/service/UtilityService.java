package com.smaile.health.service;

import com.smaile.health.model.CountryDTO;
import com.smaile.health.model.MarketDTO;
import com.smaile.health.model.MarketSegmentDTO;
import com.smaile.health.model.ProfessionalSpecialtyDTO;

import java.util.List;

public interface UtilityService {

    List<MarketDTO> getAllMarket();

    List<CountryDTO> getAllCountry();

    List<MarketSegmentDTO> getAllMarketSegment();

    List<ProfessionalSpecialtyDTO> getAllProfessionalSpecialty();

}