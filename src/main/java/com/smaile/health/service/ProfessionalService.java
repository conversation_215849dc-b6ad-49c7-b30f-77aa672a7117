package com.smaile.health.service;

import com.smaile.health.model.ProfessionalDTO;
import com.smaile.health.model.ProfessionalSummaryDTO;
import com.smaile.health.model.RegisterProfessionalFormDTO;
import com.smaile.health.model.RegisterProfessionalPresignedUrl;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.response.PageResponse;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

public interface ProfessionalService {

    UUID create(RegisterProfessionalFormDTO registerProfessionalFormDTO) throws IOException;

    List<RegisterProfessionalPresignedUrl> createPresignedUrl() throws IOException;

    PageResponse<ProfessionalDTO> query(String search, List<Filter> filters, Pageable pageable);

    ProfessionalDTO detail(UUID professionalId);

    ProfessionalDTO approve(UUID professionalId);

    ProfessionalDTO deny(UUID professionalId);

    ProfessionalSummaryDTO summary();

}