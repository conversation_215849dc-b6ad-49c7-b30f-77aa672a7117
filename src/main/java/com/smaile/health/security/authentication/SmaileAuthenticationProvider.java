package com.smaile.health.security.authentication;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

/**
 * AuthenticationProvider implementation for SMAILE Health platform.
 * This provider handles authentication for users coming through Keycloak integration
 * and builds comprehensive organization-specific permission contexts.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmaileAuthenticationProvider implements AuthenticationProvider {

    private final SmaileUserDetailsService smaileUserDetailsService;

    /**
     * Performs authentication for SMAILE users.
     * <p>
     * This method validates the user credentials (Keycloak ID) and builds
     * a comprehensive authentication context with organization-specific permissions.
     *
     * @param authentication The authentication request containing user credentials
     * @return SmaileAuthentication with complete permission context
     * @throws AuthenticationException if authentication fails
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        log.debug("Processing Keycloak header authentication");

        if (!(authentication instanceof SmaileAuthenticationToken authToken)) {
            log.debug("Authentication token is not SmaileAuthenticationToken");
            return null;
        }

        String keycloakUserId = authToken.getKeycloakId();

        if (keycloakUserId == null || keycloakUserId.trim().isEmpty()) {
            throw new BadCredentialsException("Keycloak user ID cannot be empty");
        }

        try {
            SmaileUserDetails userDetails = (SmaileUserDetails) smaileUserDetailsService.loadUserByUsername(keycloakUserId);

            if (!userDetails.isEnabled()) {
                throw new DisabledException("User account is disabled");
            }

            if (!userDetails.isAccountNonLocked()) {
                throw new DisabledException("User account is locked");
            }

            if (!userDetails.isAccountNonExpired()) {
                throw new DisabledException("User account has expired");
            }
            SmaileAuthenticationToken authenticatedToken = new SmaileAuthenticationToken(
                    keycloakUserId,
                    userDetails,
                    userDetails.getAuthorities()
            );

            log.debug("Successfully authenticated user: {} (Keycloak ID: {}) with authorities: {}",
                    userDetails.getUsername(), keycloakUserId, userDetails.getAuthorities());

            return authenticatedToken;

        } catch (UsernameNotFoundException e) {
            log.warn("User not found for Keycloak ID: {}", keycloakUserId);
            throw new BadCredentialsException("Invalid user", e);
        } catch (DisabledException e) {
            log.warn("User disabled for Keycloak ID: {}", keycloakUserId);
            throw e;
        } catch (Exception e) {
            log.error("Authentication error for Keycloak ID {}: {}", keycloakUserId, e.getMessage(), e);
            throw new BadCredentialsException("Authentication failed", e);
        }
    }

    /**
     * Determines if this provider supports the given authentication type.
     *
     * @param authentication The authentication class to check
     * @return true if this provider supports the authentication type
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return SmaileAuthenticationToken.class.isAssignableFrom(authentication);
    }

}
