package com.smaile.health.security.authentication;

import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Objects;

/**
 * Custom authentication token for SMAILE Health platform.
 * <p>
 * This token represents an authentication request containing user credentials
 * from Keycloak integration (headers: x-forwarded-smaile-user, x-forwarded-email, etc.).
 * <p>
 * The token is used by SmaileAuthenticationProvider to authenticate users
 * and build comprehensive permission contexts.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Getter
public class SmaileAuthenticationToken extends AbstractAuthenticationToken {

    @Getter
    private final String keycloakId;
    private final Object principal;

    /**
     * Creates an unauthenticated token with user credentials.
     *
     * @param keycloakId The Keycloak user ID (primary credential)
     */
    public SmaileAuthenticationToken(String keycloakId) {
        super(null);
        this.keycloakId = keycloakId;
        this.principal = keycloakId;
        setAuthenticated(false);
    }

    /**
     * Creates an authenticated token with user credentials and authorities.
     *
     * @param keycloakId  The Keycloak user ID (primary credential)
     * @param principal   The user principal (Keycloak ID)
     * @param authorities The user's granted authorities
     */
    public SmaileAuthenticationToken(String keycloakId,
                                     Object principal,
                                     Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.keycloakId = keycloakId;
        this.principal = principal;
        setAuthenticated(true);
    }

    /**
     * Returns the null value since we don't have any credentials to authenticate the user.
     *
     * @return The null value
     */
    @Override
    public Object getCredentials() {
        return null;
    }

    /**
     * Returns the principal (Keycloak ID) that identifies the user.
     *
     * @return The user details
     */
    @Override
    public Object getPrincipal() {
        return principal;
    }

    @Override
    public String getName() {
        return keycloakId;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof SmaileAuthenticationToken that)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        return Objects.equals(keycloakId, that.keycloakId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), keycloakId);
    }

}
