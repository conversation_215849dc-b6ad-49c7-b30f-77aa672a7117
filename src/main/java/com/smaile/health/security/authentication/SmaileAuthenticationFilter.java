package com.smaile.health.security.authentication;

import com.smaile.health.config.WebSecurityConfig;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Authentication filter for SMAILE Health platform.
 * <p>
 * Authentication Flow:
 * 1. Extract credentials from request headers (Keycloak integration)
 * 2. Create SmaileAuthenticationToken
 * 3. Delegate to SmaileAuthenticationProvider for authentication
 * 4. Set authenticated context in SecurityContextHolder
 * <p>
 * Security Features:
 * - Request validation and sanitization
 * - Whitelist checking for public endpoints
 * - Proper error handling and logging
 * - Integration with Spring Security authentication flow
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmaileAuthenticationFilter extends OncePerRequestFilter {

    private static final String X_FORWARDED_USER = "x-forwarded-smaile-user";
    private static final String X_FORWARDED_EMAIL = "x-forwarded-email";
    private static final String X_FORWARDED_PREFERRED_USERNAME = "x-forwarded-preferred-username";

    private final AuthenticationManager authenticationManager;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    /**
     * Performs authentication for each request.
     *
     * @param request     The HTTP request
     * @param response    The HTTP response
     * @param filterChain The filter chain
     * @throws ServletException if servlet processing fails
     * @throws IOException      if I/O processing fails
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String requestUri = request.getRequestURI();
        String method = request.getMethod();

        log.debug("Processing authentication for {} {}", method, requestUri);

        try {
            if (isWhitelisted(requestUri)) {
                log.debug("Request {} is whitelisted, skipping authentication", requestUri);
                filterChain.doFilter(request, response);
                return;
            }
            Authentication existingAuth = SecurityContextHolder.getContext().getAuthentication();
            if (existingAuth != null && existingAuth.isAuthenticated()) {
                filterChain.doFilter(request, response);
                return;
            }
            String keycloakId = request.getHeader(X_FORWARDED_USER);
            if (keycloakId == null) {
                handleAuthenticationFailure(request, response, new BadCredentialsException("Keycloak ID is required"));
                return;
            }
            SmaileAuthenticationToken authRequest = new SmaileAuthenticationToken(keycloakId);
            Authentication authentication = authenticationManager.authenticate(authRequest);
            SecurityContextHolder.getContext().setAuthentication(authentication);
            log.debug("Successfully authenticated user with Keycloak ID: {}", keycloakId);
        } catch (AuthenticationException e) {
            log.warn("Authentication failed: {}", e.getMessage());
            SecurityContextHolder.clearContext();
            handleAuthenticationFailure(request, response, e);
        } catch (Exception e) {
            log.error("Unexpected error during authentication: {}", e.getMessage(), e);
            SecurityContextHolder.clearContext();
            return;
        }
        filterChain.doFilter(request, response);
    }

    /**
     * Handles authentication failures.
     *
     * @param request   The HTTP request
     * @param response  The HTTP response
     * @param exception The authentication exception
     * @throws IOException if I/O processing fails
     */
    private void handleAuthenticationFailure(HttpServletRequest request,
                                             HttpServletResponse response,
                                             AuthenticationException exception) throws IOException {

        SecurityContextHolder.clearContext();

        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.getWriter()
                .write("{\"error\":\"Authentication failed\",\"message\":\"" + exception.getMessage() + "\"}");

        log.warn("Authentication failed for request {} {}: {}", request.getMethod(), request.getRequestURI(),
                exception.getMessage());
    }

    /**
     * Checks if the request URI is whitelisted (public endpoints).
     *
     * @param uri The request URI
     * @return true if whitelisted, false otherwise
     */
    private boolean isWhitelisted(String uri) {
        if (uri == null) {
            return false;
        }
        String pathToCheck = uri;
        if (!contextPath.isEmpty() && uri.startsWith(contextPath)) {
            pathToCheck = uri.substring(contextPath.length());
        }

        for (String path : WebSecurityConfig.WHITELIST) {
            String cleanPath = path.replace("/**", "");
            if (pathToCheck.startsWith(cleanPath)) {
                return true;
            }
        }

        return false;
    }

}
