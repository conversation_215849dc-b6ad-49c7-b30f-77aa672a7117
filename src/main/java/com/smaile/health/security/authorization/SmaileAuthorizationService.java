package com.smaile.health.security.authorization;

import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.util.SecurityContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * Service responsible for authorization decisions in the SMAILE Health platform.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Service("smaileAuthorizationService")
@RequiredArgsConstructor
@Slf4j
public class SmaileAuthorizationService {

    /**
     * Checks if the authenticated user has inherited permission of the parent organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @return true if user has inherited permission of the parent organization, false otherwise
     */
    public boolean isInheritPermissionOrganization(Authentication authentication,
                                                   UUID organizationId,
                                                   String permission) {
        if (isInvalidAuthentication(authentication)) {
            return false;
        }
        return SecurityContextUtils.getOwnerOrganizationContexts()
                .hasPermission(permission) && SecurityContextUtils.getOrganizationContexts()
                .containsKey(organizationId);
    }

    /**
     * Checks if the authenticated user has a specific permission in an organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @param permission     The permission to verify
     * @return true if user has the permission, false otherwise
     */
    public boolean hasPermissionInOrganization(Authentication authentication, UUID organizationId, String permission) {
        if (isInvalidInput(authentication, organizationId, permission)) {
            return false;
        }

        try {
            OrganizationPermissionContext context = SecurityContextUtils.getOrganizationContext(organizationId);
            boolean hasPermission = context.getPermissions().stream().anyMatch(ctx -> ctx.contains(permission));

            log.debug("Permission check: user={}, org={}, permission={}, result={}", authentication.getName(),
                    organizationId, permission, hasPermission);

            return hasPermission;
        } catch (Exception e) {
            log.warn("Error checking permission for user {} in organization {}: {}", authentication.getName(),
                    organizationId, e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the authenticated user has a specific role in an organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @param role           The role to verify
     * @return true if user has the role, false otherwise
     */
    public boolean hasRoleInOrganization(Authentication authentication, UUID organizationId, String role) {
        if (isInvalidInput(authentication, organizationId, role)) {
            return false;
        }

        try {
            OrganizationPermissionContext context = SecurityContextUtils.getOrganizationContext(organizationId);
            boolean hasRole = context.getRole().equals(role);

            log.debug("Role check: user={}, org={}, role={}, result={}", authentication.getName(), organizationId, role,
                    hasRole);

            return hasRole;
        } catch (Exception e) {
            log.warn("Error checking role for user {} in organization {}: {}", authentication.getName(), organizationId,
                    e.getMessage());
            return false;
        }
    }

    /**
     * Validates the input parameters for permission/role checks.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID
     * @param value          The permission/role value to check
     * @return true if any input is invalid, false otherwise
     */
    private boolean isInvalidInput(Authentication authentication, UUID organizationId, String value) {
        if (isInvalidAuthentication(authentication)) {
            return true;
        }

        if (organizationId == null) {
            log.debug("Organization ID cannot be null");
            return true;
        }

        if (value == null || value.trim().isEmpty()) {
            log.debug("Permission/role value cannot be null or empty");
            return true;
        }

        return false;
    }

    private static boolean isInvalidAuthentication(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.debug("Invalid authentication object provided");
            return true;
        }
        return false;
    }

}
