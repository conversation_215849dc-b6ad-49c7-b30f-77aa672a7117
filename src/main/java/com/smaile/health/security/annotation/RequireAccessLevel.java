package com.smaile.health.security.annotation;

import com.smaile.health.security.rbac.RoleBasedAccessControlService.AccessLevel;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for method-level access level checking.
 * <p>
 * This annotation can be used on controller methods or service methods
 * to declaratively specify minimum required access level for access.
 * <p>
 * Access levels are hierarchical:
 * SUPER_ADMIN > ADMIN > MANAGER > MANAGE > WRITE > READ > INHERITED > LIMITED > NONE
 * <p>
 * Usage Examples:
 * <p>
 * Require admin access:
 *
 * <AUTHOR>
 * @version 1.0
 * @RequireAccessLevel(AccessLevel.ADMIN) Require at least write access:
 * @RequireAccessLevel(AccessLevel.WRITE) Organization-specific access level:
 * @RequireAccessLevel(value = AccessLevel.ADMIN, organizationParam = "organizationId")
 * @since 2025/08/26
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireAccessLevel {

    /**
     * The minimum access level required.
     */
    AccessLevel value();

    /**
     * Name of the method parameter that contains the organization ID.
     * If specified, the access level check will be organization-specific.
     * If not specified, the check will look for the highest access level across all organizations.
     */
    String organizationParam() default "";

    /**
     * Error message to return when access level is insufficient.
     * If not specified, a default message will be used.
     */
    String message() default "";

}
