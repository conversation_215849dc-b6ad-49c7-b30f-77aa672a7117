package com.smaile.health.security.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for method-level permission checking.
 * <p>
 * This annotation can be used on controller methods or service methods
 * to declaratively specify required permissions for access.
 * <p>
 * Usage Examples:
 * <p>
 * Single permission:
 *
 * <AUTHOR>
 * @version 1.0
 * @RequirePermission(resource = "user", action = "create")
 * <p>
 * Multiple permissions (ANY):
 * @RequirePermission(resource = "user", action = {"create", "update"}, logic = Logic.ANY)
 * <p>
 * Multiple permissions (ALL):
 * @RequirePermission(resource = "user", action = {"read", "list"}, logic = Logic.ALL)
 * <p>
 * Organization-specific:
 * @RequirePermission(resource = "report", action = "create", organizationParam = "organizationId")
 * @since 2025/08/26
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {

    /**
     * The resource type being accessed.
     * Examples: "user", "organization", "report", "patient"
     */
    String resource();

    /**
     * The action(s) being performed on the resource.
     * Examples: "create", "read", "update", "delete", "list", "manage"
     */
    String[] action();

    /**
     * Logic for combining multiple actions.
     * ANY: User needs at least one of the specified permissions
     * ALL: User needs all the specified permissions
     */
    Logic logic() default Logic.ANY;

    /**
     * Name of the method parameter that contains the organization ID.
     * If specified, the permission check will be organization-specific.
     * If not specified, the permission check will be global.
     */
    String organizationParam() default "";

    /**
     * Error message to return when permission is denied.
     * If not specified, a default message will be used.
     */
    String message() default "";

    /**
     * Logic enumeration for combining multiple permissions.
     */
    enum Logic {
        ANY,
        ALL
    }

}
