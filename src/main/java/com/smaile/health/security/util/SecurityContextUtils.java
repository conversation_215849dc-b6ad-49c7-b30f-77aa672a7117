package com.smaile.health.security.util;

import com.smaile.health.domain.projection.UserInfo;
import com.smaile.health.security.authentication.SmaileAuthenticationToken;
import com.smaile.health.security.authentication.SmaileUserDetails;
import com.smaile.health.security.context.OrganizationPermissionContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Utility class for easy access to security context and permission checking.
 */
@Slf4j
public final class SecurityContextUtils {

    private SecurityContextUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Get current SmaileAuthenticationToken
     */
    public static Optional<SmaileAuthenticationToken> getCurrentAuthentication() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth instanceof SmaileAuthenticationToken smaileAuthentication && smaileAuthentication.isAuthenticated()) {
                return Optional.of(smaileAuthentication);
            }
        } catch (Exception e) {
            log.debug("Failed to get current authentication", e);
        }
        return Optional.empty();
    }

    /**
     * Get current SmaileUserDetails
     */
    public static SmaileUserDetails getCurrentUserDetails() {
        return getCurrentAuthentication().map(o -> (SmaileUserDetails) o.getPrincipal())
                .orElseThrow(() -> new AccessDeniedException("User not authenticated"));
    }

    /**
     * Get current UserInfo
     */
    public static UserInfo getCurrentUserInfo() {
        return getCurrentUserDetails().getUser();
    }

    /**
     * Get permission context for specific organization
     */
    public static OrganizationPermissionContext getOrganizationContext(UUID organizationId) {
        if (organizationId == null || !getOrganizationContexts().containsKey(organizationId)) {
            throw new AccessDeniedException("Organization ID cannot be null");
        }
        return getOrganizationContexts().get(organizationId);
    }

    /**
     * Get owner organization ID
     */
    public static UUID getOwnerOrganizationId() {
        UUID ownerOrganizationId = getCurrentUserDetails().getOwnerOrganizationId();
        if (ownerOrganizationId == null) {
            throw new AccessDeniedException("User is not associated with any organization");
        }
        return ownerOrganizationId;
    }

    /**
     * Check if current user is super admin
     */
    public static boolean isSuperAdmin() {
        return getCurrentUserDetails().isSuperAdmin();
    }

    /**
     * Check if current user has a specific role
     */
    public static boolean hasRole(String role) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof SmaileAuthenticationToken && authentication.isAuthenticated()) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(role));
        }
        return false;
    }

    /**
     * Get all organization contexts
     */
    public static Map<UUID, OrganizationPermissionContext> getOrganizationContexts() {
        return getCurrentUserDetails().getOrganizationPermissionContexts();
    }

    /**
     * Get owner organization context
     */
    public static OrganizationPermissionContext getOwnerOrganizationContexts() {
        SmaileUserDetails currentUserDetails = getCurrentUserDetails();
        UUID ownerOrganizationId = getOwnerOrganizationId();
        return currentUserDetails.getOrganizationPermissionContexts().get(ownerOrganizationId);
    }

}
