package com.smaile.health.security.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * Represents a structured security permission with resource, action, and scope.
 * This is an immutable value object that encapsulates permission logic.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmaileSecurityPermission {

    private Resource resource;
    private Action action;
    private Scope scope;
    private String condition;

    /**
     * Create a permission from string format: resource:action:scope[:condition]
     */
    public static SmaileSecurityPermission fromString(String permissionString) {
        if (permissionString == null || permissionString.trim().isEmpty()) {
            throw new IllegalArgumentException("Permission string cannot be null or empty");
        }

        String[] parts = permissionString.split(":");
        if (parts.length < 3 || parts.length > 4) {
            throw new IllegalArgumentException(
                    "Invalid permission format. Expected: resource:action:scope[:condition]");
        }

        Resource resource = Resource.fromCode(parts[0]);
        Action action = Action.fromCode(parts[1]);
        Scope scope = Scope.fromCode(parts[2]);
        String condition = parts.length == 4 ? parts[3] : null;

        return SmaileSecurityPermission.builder()
                .resource(resource)
                .action(action)
                .scope(scope)
                .condition(condition)
                .build();
    }

    /**
     * Create a wildcard permission for a resource
     */
    public static SmaileSecurityPermission wildcard(Resource resource, Scope scope) {
        return SmaileSecurityPermission.builder()
                .resource(resource)
                .action(Action.MANAGE)
                .scope(scope)
                .build();
    }

    /**
     * Convert permission to string format
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(resource.getCode())
                .append(":")
                .append(action.getCode())
                .append(":")
                .append(scope.getCode());

        if (condition != null && !condition.trim().isEmpty()) {
            sb.append(":").append(condition);
        }

        return sb.toString();
    }

    /**
     * Check if this permission implies another permission
     */
    public boolean implies(SmaileSecurityPermission other) {
        if (other == null) {
            return false;
        }

        // Resource must match exactly or be a wildcard
        if (!this.resource.equals(other.resource)) {
            return false;
        }

        // Action must match exactly or this must be MANAGE (which implies all actions)
        if (!this.action.equals(other.action) && this.action != Action.MANAGE) {
            return false;
        }

        // Scope must include the other scope
        if (!this.scope.includes(other.scope)) {
            return false;
        }

        if (this.condition != null && other.condition != null) {
            return this.condition.equals(other.condition);
        } else {
            return this.condition == null;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SmaileSecurityPermission that = (SmaileSecurityPermission) o;
        return resource == that.resource &&
                action == that.action &&
                scope == that.scope &&
                Objects.equals(condition, that.condition);
    }

    @Override
    public int hashCode() {
        return Objects.hash(resource, action, scope, condition);
    }

}
