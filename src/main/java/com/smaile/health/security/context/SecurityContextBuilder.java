package com.smaile.health.security.context;

import com.smaile.health.domain.projection.OrganizationInfo;
import com.smaile.health.domain.projection.PermissionInfo;
import com.smaile.health.domain.projection.RoleInfo;
import com.smaile.health.domain.projection.UserInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service responsible for building security context with permissions for each organization
 * after successful authentication.
 */
@Slf4j
public final class SecurityContextBuilder {

    private SecurityContextBuilder() {
    }

    /**
     * Build organization-specific permission contexts for a user
     */
    public static Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(UserInfo user) {
        log.debug("Building permission contexts for user: {}", user.getEmail());
        Map<UUID, OrganizationPermissionContext> contexts = new HashMap<>();
        // Build context for owner organization
        OrganizationInfo ownerOrganization = user.getOwnerOrganization();
        contexts.put(ownerOrganization.getId(),
                buildPermissionContext(ownerOrganization, ownerOrganization.getRole(), true));
        // Build context for delegate organizations
        user.getDelegateOrganizations().forEach(organization -> contexts.put(organization.getId(),
                buildPermissionContext(organization, organization.getRole(), false)));
        log.debug("Built {} permission contexts for user: {}", contexts.size(), user.getEmail());
        return contexts;
    }

    /**
     * Build permission context for direct role assignments in an organization
     */
    private static OrganizationPermissionContext buildPermissionContext(OrganizationInfo organization,
                                                                        RoleInfo role,
                                                                        boolean isOwner) {

        return OrganizationPermissionContext.builder()
                .organizationId(organization.getId())
                .organizationName(organization.getName())
                .isOwner(isOwner)
                .permissions(getPermissions(role))
                .role(role.getCode()).build();
    }

    /**
     * Get permissions for a role
     */
    private static Set<String> getPermissions(RoleInfo role) {
        return role.getPermissions().stream()
                .map(SecurityContextBuilder::formatPermission)
                .collect(Collectors.toSet());
    }

    /**
     * Format permission as string
     */
    private static String formatPermission(PermissionInfo permission) {
        StringBuilder sb = new StringBuilder();

        if (permission.getResource() != null) {
            sb.append(permission.getResource());
        }

        if (permission.getSubResource() != null) {
            sb.append(":").append(permission.getSubResource());
        }

        if (permission.getAction() != null) {
            sb.append(":").append(permission.getAction());
        }

        return sb.toString();
    }

}
