//package com.smaile.health.security.oauth2;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.smaile.health.model.response.BaseResponse;
//import jakarta.servlet.ServletException;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.security.access.AccessDeniedException;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.security.web.access.AccessDeniedHandler;
//import org.springframework.stereotype.Component;
//
//import java.io.IOException;
//import java.time.Instant;
//import java.util.LinkedHashMap;
//import java.util.Map;
//
/// **
// * Custom OAuth2 Access Denied Handler for handling authorization failures.
// * <p>
// * This handler manages access denied scenarios when users have valid authentication
// * but lack sufficient permissions for the requested resource.
// * <p>
// * Scenarios Handled:
// * - Insufficient role/authority for endpoint access
// * - Organization-specific permission failures
// * - Method-level security violations (@PreAuthorize)
// * - Resource-specific access control failures
// *
// * <AUTHOR>
// * @version 1.0
// * @since 2025/08/27
// */
//@Component("oauth2AccessDeniedHandler")
//@RequiredArgsConstructor
//@Slf4j
//public class OAuth2AccessDeniedHandler implements AccessDeniedHandler {
//
//    private final ObjectMapper objectMapper;
//
//    @Override
//    public void handle(HttpServletRequest request, HttpServletResponse response,
//                      AccessDeniedException accessDeniedException) throws IOException, ServletException {
//
//        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//        String username = authentication != null ? authentication.getName() : "anonymous";
//
//        log.warn("Access denied for user '{}' on {} {}: {}",
//                username, request.getMethod(), request.getRequestURI(), accessDeniedException.getMessage());
//
//        response.setStatus(HttpStatus.FORBIDDEN.value());
//        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
//        response.setCharacterEncoding("UTF-8");
//
//        Map<String, Object> errorResponse = createErrorResponse(request, accessDeniedException, authentication);
//
//        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
//        response.getWriter().flush();
//    }
//
//    /**
//     * Creates a structured error response for access denied scenarios.
//     */
//    private Map<String, Object> createErrorResponse(HttpServletRequest request,
//                                                   AccessDeniedException accessDeniedException,
//                                                   Authentication authentication) {
//        Map<String, Object> errorResponse = new LinkedHashMap<>();
//
//        // Basic error information
//        errorResponse.put("timestamp", Instant.now().toString());
//        errorResponse.put("status", HttpStatus.FORBIDDEN.value());
//        errorResponse.put("error", "Forbidden");
//        errorResponse.put("path", request.getRequestURI());
//        errorResponse.put("method", request.getMethod());
//
//        // Access control specific details
//        errorResponse.put("error_code", "access_denied");
//        errorResponse.put("error_description", "Insufficient permissions to access this resource");
//        errorResponse.put("message", accessDeniedException.getMessage());
//
//        // Add user context if available
//        if (authentication != null && authentication.isAuthenticated()) {
//            errorResponse.put("user", authentication.getName());
//            errorResponse.put("authorities", authentication.getAuthorities().stream()
//                    .map(authority -> authority.getAuthority())
//                    .toList());
//        }
//
//        // Add helpful information for developers
//        errorResponse.put("help", "Ensure your token has the required roles/permissions for this endpoint");
//
//        // Wrap in BaseResponse format for consistency
//        BaseResponse<Object> baseResponse = BaseResponse.builder()
//                .success(false)
//                .message("Access denied")
//                .data(errorResponse)
//                .build();
//
//        Map<String, Object> response = new LinkedHashMap<>();
//        response.put("success", baseResponse.isSuccess());
//        response.put("message", baseResponse.getMessage());
//        response.put("data", baseResponse.getData());
//
//        return response;
//    }
//}
