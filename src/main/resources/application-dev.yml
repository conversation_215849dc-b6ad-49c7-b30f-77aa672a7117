spring:
  datasource:
    url: ${database.url:*********************************************}
    username: ${database.username:lukepham}
    password: ${database.password:lukepham}
  jpa:
    properties:
      hibernate:
        format_sql: true
        show_sql: true
  output.ansi.enabled: ALWAYS
  liquibase:
    contexts: development

server:
  port: ${SERVER_PORT:8000}

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:https://iam.smaile.egs-dev.site}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID:smaile-be}
  client-secret: ${IAM_CLIENT_SECRET:B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB}

management:
  endpoint:
    health:
      probes:
        enabled: true
  endpoints:
    web:
      exposure:
        include: health,info,readiness,liveness
  metrics:
    tags:
      environment: development

logging:
  level:
    com.smaile.health: DEBUG
    org.springframework.security: DEBUG
    liquibase: DEBUG
    root: INFO