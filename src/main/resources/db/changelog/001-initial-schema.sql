--liquibase formatted sql

--changeset toanpham:001-add-uaa-tables
CREATE TABLE organizations
(
    id              UUID PRIMARY KEY,
    name            VA<PERSON>HAR(255)             NOT NULL,
    type            VARCHAR(100)             NOT NULL,
    registration_number VARCHAR(255),
    status VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    address TEXT,
    version         BIGINT DEFAULT 0,
    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL
);

CREATE TABLE users
(
    id              UUID PRIMARY KEY,
    keycloak_id     VARCHAR(255)             NOT NULL,
    email           VARCHAR(255)             NOT NULL,
    full_name       VARCHAR(255)             NOT NULL,
    phone           VARCHAR(50),
    status          VARCHAR(50),
    version         BIGINT DEFAULT 0,
    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL
);

CREATE TABLE roles
(
    id              UUID PRIMARY KEY,
    name            VA<PERSON><PERSON><PERSON>(255)             NOT NULL UNIQUE,
    code            VA<PERSON>HAR(16)             NOT NULL UNIQUE,
    description     TEXT,
    scope           VARCHAR(100),
    status          VARCHAR(50),
    version         BIGINT DEFAULT 0,
    date_created    TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated    TIMESTAMP WITH TIME ZONE NOT NULL
);

CREATE TABLE permissions (
	id uuid PRIMARY KEY,
	"name" varchar(255) NOT NULL,
	resource varchar(255) NOT NULL,
	sub_resource varchar(255) NOT NULL,
	"action" varchar(255) NOT NULL,
	description text NULL,
	"version" BIGINT DEFAULT 0,
	date_created timestamp with time zone NOT NULL,
	last_updated timestamp with time zone NOT NULL
);


CREATE TABLE users_organizations
(
    id UUID PRIMARY KEY,
    user_id         UUID NOT NULL,
    organization_id UUID NOT NULL,
    status VARCHAR(100),
    start_time timestamp with time zone,
    end_time timestamp with time zone,
    date_created timestamp with time zone NOT NULL,
    last_updated timestamp with time zone NOT NULL
);

CREATE TABLE users_roles
(
    id UUID PRIMARY KEY,
    user_organization_id UUID NOT NULL,
    role_id UUID NOT NULL,
    status varchar(100),
    start_time timestamp with time zone NOT NULL,
    end_time timestamp with time zone,
    assigned_by UUID,
    date_created timestamp with time zone NOT NULL,
    last_updated timestamp with time zone NOT NULL
);

CREATE TABLE roles_permissions
(
    id UUID PRIMARY KEY,
    role_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    date_created timestamp with time zone NOT NULL,
    last_updated timestamp with time zone NOT NULL
);

ALTER TABLE users_organizations
    ADD CONSTRAINT fk_users_organizations_user
        FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE users_organizations
    ADD CONSTRAINT fk_users_organizations_organization
        FOREIGN KEY (organization_id) REFERENCES organizations (id);

ALTER TABLE users_roles
    ADD CONSTRAINT fk_users_roles_user_organization
        FOREIGN KEY (user_organization_id) REFERENCES users_organizations (id);

ALTER TABLE users_roles
    ADD CONSTRAINT fk_users_roles_role
        FOREIGN KEY (role_id) REFERENCES roles (id);

ALTER TABLE roles_permissions
    ADD CONSTRAINT fk_roles_permissions_role
        FOREIGN KEY (role_id) REFERENCES roles (id);

ALTER TABLE roles_permissions
    ADD CONSTRAINT fk_roles_permissions_permission
        FOREIGN KEY (permission_id) REFERENCES permissions (id);

CREATE INDEX idx_organizations_name ON organizations(name);
CREATE INDEX idx_organizations_type ON organizations(type);

CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_scope ON roles(scope);
CREATE INDEX idx_roles_status ON roles(status);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);

CREATE INDEX idx_users_roles_user_organization ON users_roles(user_organization_id);
CREATE INDEX idx_users_roles_role ON users_roles(role_id);

CREATE INDEX idx_users_organizations_user ON users_organizations(user_id);
CREATE INDEX idx_users_organizations_organization ON users_organizations(organization_id);