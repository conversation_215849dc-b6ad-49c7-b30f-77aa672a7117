container:
  # ALWAYS specify max heap size (in percentage of available mem)
  # Add other arguments if necessary
  java_opts: "-XX:MaxRAMPercentage=85"

  readinessProbe:
    initialDelaySeconds: 20
    periodSeconds: 10

  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30

resources:
  requests:
    memory: 1Gi
    cpu: 512m

  limits:
    memory: 1Gi
    cpu: 1000m

# Application configuration (this will override the config inside the docker image)
application:
  server:
    port: 8000

  database:
    url: ******************************************
    username:
    password:

  iam:
    endpoint: http://localhost:8080
    realm: your-realm
    client-id: your-client
    client-secret: your-secret

# define the domain (it will be http://subDomain.baseDomain) that other service in the cluster can access to
# The domain can be replaced by your real domain and then it can be accessed on your internet
global:
  baseDomain: egs.com
  subDomain: api

clusterDomain: svc.cluster.local

contextPath: /api/v1

image:
  repository:
  pullPolicy:

# allow you to override the chart name
# DO NOT forget to update secret keys as well

#nameOverride: egs-app-ext

#imagePullSecrets: [ name: secret-name  ]
