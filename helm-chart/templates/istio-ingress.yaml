apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: {{ include "chart.name" . }}-gateway
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - {{ .Values.global.subDomain }}.{{ .Values.global.baseDomain }}
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ include "chart.name" . }}-virtual-service
spec:
  hosts:
  - {{ .Values.global.subDomain }}.{{ .Values.global.baseDomain }}
  gateways:
  - {{ include "chart.name" . }}-gateway
  http:
  - match:
    - uri:
        prefix: {{ .Values.contextPath }}
    route:
    - destination:
        host: {{ include "chart.name" . }}.{{ .Release.Namespace }}.{{ .Values.clusterDomain }}
        port:
          number: 80
---
apiVersion: security.istio.io/v1
kind: RequestAuthentication
metadata:
  name: {{ include "chart.name" . }}-request-authentication
spec:
  selector:
    matchLabels:
      istio: ingressgateway
  jwtRules:
    - issuer: "{{ .Values.application.iam.host }}/realms/{{ .Values.application.iam.realm }}"
      jwksUri: "{{ .Values.application.iam.host }}/realms/{{ .Values.application.iam.realm }}/protocol/openid-connect/certs"
      outputClaimToHeaders:
        - header: "x-forwarded-smaile-user"
          claim: "sub"